"""
测试导入提示词模板
"""

def test_import():
    """测试导入提示词模板"""
    print("测试导入提示词模板...")
    
    try:
        # 测试导入提示词
        from resources.prompt import enhanced_prompt, enhanced_analysis_prompt
        print("成功导入enhanced_prompt和enhanced_analysis_prompt!")
        
        # 测试导入其他模块
        from resources.prompt_based_generator import generate_prompt_based_content
        print("成功导入generate_prompt_based_content!")
        
        # 测试导入配置
        from config import SystemConfig
        print(f"成功导入SystemConfig! 默认API密钥: {SystemConfig.DEFAULT_API_KEY[:5]}...")
        print(f"默认视觉模型: {SystemConfig.DEFAULT_MODEL}")
        print(f"内容生成模型: {SystemConfig.CONTENT_GEN_MODEL}")
        
        print("\n所有导入测试通过!")
    except ImportError as e:
        print(f"导入错误: {e}")
    except Exception as e:
        print(f"其他错误: {e}")

if __name__ == "__main__":
    test_import() 