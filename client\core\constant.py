"""
污染信息抵抗系统 - 常量和枚举定义
集中管理系统中的各种枚举类型和固定常量
"""

from enum import Enum, auto
import os

# 根目录路径
ROOT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))  # 上一级是client目录
RESOURCES_DIR = os.path.join(ROOT_DIR, "resources")

# ======== 提示词模式 ========
class PromptMode(Enum):
    """提示词模式"""
    ENHANCED = auto()   # 增强模式 - 更专业的污染信息分析
    DIRTY = auto()      # 脏话模式 - 更情绪化的污染信息抨击

# ======== 遮罩类型 ========
class OverlayType(Enum):
    """遮罩类型"""
    EVIL = "Evil"       # 污染信息
    NORMAL = "Normal"   # 普通信息
    GOOD = "Good"       # 有价值信息

# ======== 抵抗模式 ========
class ResistanceMode(Enum):
    """抵抗模式"""
    MASK = "MASK"           # 遮罩模式 - 使用图标覆盖污染内容
    PROMPT = "PROMPT"       # 提示模式 - 显示文字提示
    OBSERVE = "OBSERVE"     # 观察模式 - 仅标记不遮挡

# ======== 抵抗方案选择模式 ========
class ResistanceSelectionMode(Enum):
    """抵抗方案选择模式"""
    RANDOM = "RANDOM"              # 随机选择方案（包含概率选择高级方案）
    BASIC_ONLY = "BASIC_ONLY"      # 仅使用基础抵抗方案
    ADVANCED_ONLY = "ADVANCED_ONLY" # 优先使用高级抵抗方案
    CREATIVE_ONLY = "CREATIVE_ONLY" # 优先使用创意抵抗方案
    ACADEMIC_ONLY = "ACADEMIC_ONLY" # 优先使用学术解构方案
    ART_ONLY = "ART_ONLY"          # 优先使用终极艺术武器方案
    
    @classmethod
    def get_display_name(cls, mode_enum):
        display_names = {
            cls.RANDOM: "随机选择",
            cls.BASIC_ONLY: "仅基础抵抗",
            cls.ADVANCED_ONLY: "优先高级抵抗",
            cls.CREATIVE_ONLY: "优先创意抵抗",
            cls.ACADEMIC_ONLY: "优先学术解构",
            cls.ART_ONLY: "优先艺术武器"
        }
        return display_names.get(mode_enum, "未知模式")

# ======== 图像下采样率 ========
class DownsampleRate(Enum):
    """图像下采样率"""
    HIGH_QUALITY = 1.0    # 高质量，无下采样
    BALANCED = 0.5        # 平衡模式
    PERFORMANCE = 0.25    # 性能模式

# ======== 污染类型 ========
class PollutionType(Enum):
    """污染类型"""
    CELEBRITY = "CELEBRITY"        # 流量明星
    ENTERTAINMENT = "ENTERTAINMENT"  # 娱乐内容
    ADVERTISEMENT = "ADVERTISEMENT"  # 商业广告
    LOW_QUALITY = "LOW_QUALITY"    # 低智内容
    HOT_TOPIC = "HOT_TOPIC"        # 热搜话题
    
    @classmethod
    def get_display_name(cls, type_enum):
        display_names = {
            cls.CELEBRITY: "流量明星",
            cls.ENTERTAINMENT: "娱乐内容",
            cls.ADVERTISEMENT: "商业广告",
            cls.LOW_QUALITY: "低质量内容",
            cls.HOT_TOPIC: "热搜话题"
        }
        return display_names.get(type_enum, "未知污染")

# ======== 抵抗方案类型 ========
class ResistanceType(Enum):
    """抵抗方案类型"""
    BASIC = "BASIC"          # 基础抵抗
    CREATIVE = "CREATIVE"    # 创意抵抗
    ACADEMIC = "ACADEMIC"    # 学术解构
    ART = "ART"              # 终极艺术武器
    ADVANCED = "ADVANCED"    # 高级抵抗方案
    
    @classmethod
    def get_display_name(cls, type_enum):
        display_names = {
            cls.BASIC: "基础抵抗",
            cls.CREATIVE: "创意抵抗",
            cls.ACADEMIC: "学术解构",
            cls.ART: "终极艺术武器",
            cls.ADVANCED: "高级抵抗方案"
        }
        return display_names.get(type_enum, "未知类型")

# ======== 抵抗方法 ========
class ResistanceMethods(Enum):
    """抵抗方法"""
    MASK = "MASK"                    # 遮罩屏蔽
    POOP = "POOP"                    # 大便贴纸
    SKULL = "SKULL"                  # 骷髅警告
    FAKE_HISTORY = "FAKE_HISTORY"    # 明星考古假报告
    AD_CARTOON = "AD_CARTOON"        # 广告转资本论漫画
    ATTENTION_ECONOMY = "ATTENTION_ECONOMY"  # 注意力经济论文
    SYMBOL_ANALYSIS = "SYMBOL_ANALYSIS"      # 符号学分析
    COLLECTIVE_DIAGNOSIS = "COLLECTIVE_DIAGNOSIS"  # 集体癔症诊断书
    CONSUMPTION_VACCINE = "CONSUMPTION_VACCINE"    # 消费主义疫苗
    
    # 高级抵抗方案
    CELEBRITY_SYMBOL_ANALYSIS = "CELEBRITY_SYMBOL_ANALYSIS"    # 明星符号学解析
    CELEBRITY_ECONOMIC_ANALYSIS = "CELEBRITY_ECONOMIC_ANALYSIS"  # 明星产业链剖析
    CELEBRITY_PHILOSOPHICAL_REFLECTION = "CELEBRITY_PHILOSOPHICAL_REFLECTION"  # 明星崇拜哲学反思
    ENTERTAINMENT_NEWS_DECODING = "ENTERTAINMENT_NEWS_DECODING"  # 娱乐新闻叙事拆解
    AD_DESIRE_ENGINEERING = "AD_DESIRE_ENGINEERING"  # 广告欲望工程学
    CONSUMPTION_PHILOSOPHY = "CONSUMPTION_PHILOSOPHY"  # 消费主义存在论

# ======== 应用类型 ========
class AppType(Enum):
    """应用类型"""
    BROWSER = "浏览器"
    WEIBO = "微博(浏览器)"
    WECHAT = "微信"
    TWITTER = "Twitter(浏览器)"
    VSCODE = "VS Code"
    OTHER = "其他应用"
    
    @classmethod
    def is_browser_app(cls, app_type):
        """判断是否为浏览器类应用"""
        browser_apps = [cls.BROWSER, cls.WEIBO, cls.TWITTER]
        return any(app_type == app.value for app in browser_apps)

# ======== 文件资源常量 ========
class ResourcePaths:
    """文件资源路径常量"""
    MOUSE_ICON = "mouse.png"
    EAGLE_ICON = "神鹰.png"
    SKULL_ICON = "skull.png"
    POOP_ICON = "卡通大便.png"
    SMELL_POOP_ICON = "smell_shit.png"  # 添加嗅到大便的图标
    MASK_ICON = "mask.png" 