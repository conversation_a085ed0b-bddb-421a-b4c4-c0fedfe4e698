# 污染信息抵抗系统 - 实时API调用模式

## 核心改进

我们实现了基于API直接调用的提示词抵抗内容生成方式，主要改进如下：

### 1. 两步式生成流程
- 第一步：使用VLM（视觉语言模型）分析图像内容，生成详细的污染内容分析
- 第二步：使用专门的内容生成模型（qwen-max）基于分析结果生成高质量抵抗内容

### 2. API调用能力
- 实现了与通义千问大模型API的集成
- 支持VLM和纯文本LLM的调用
- 提供了错误处理和回退机制

### 3. 智能JSON解析
- 增强的JSON解析能力，可以处理各种格式的API返回结果
- 支持修复不完整或格式错误的JSON
- 提供了文本提取能力作为备选方案

### 4. 多样化的抵抗内容类型
- 哲学反思：从存在论、认识论等角度分析污染现象
- 经济分析：从价值链、注意力经济等角度解构污染内容
- 创意转换：将污染内容转化为诗歌、视觉概念和叙事重构
- 历史脉络：分析类似现象的历史演变和技术影响

### 5. 流式输出与性能优化
- 实现了流式输出，实时显示模型生成过程
- 添加了响应进度指示器，每5秒更新一次
- 实现了内容缓存机制，避免重复生成相同内容
- 简化了提示词模板和输入内容，减少token使用
- 添加了超时处理，避免长时间等待

## 响应时间测试

我们测试了不同模型和提示词长度的响应时间：

### LLM (qwen-max)
- 短提示词：约33秒
- 中等提示词：约24秒
- 长提示词：约28秒

### VLM (qwen2.5-vl-72b-instruct)
- 短提示词：约8秒
- 中等提示词：约10秒
- 长提示词：约6秒

VLM模型的响应明显快于纯文本LLM模型。

## 使用方法

### 测试API调用
```python
python tests/test_api_call.py
```

### 测试完整生成流程
```python
python tests/test_content_generation.py
```

### 测试流式输出和响应速度
```python
python tests/test_stream_speed.py
```

### 启动主应用程序
```python
python run.py
```

## 配置选项

主要配置项在`core/config.py`中：

- `DEFAULT_API_KEY`: API密钥
- `DEFAULT_VLM_MODEL`: 用于视觉分析的模型
- `CONTENT_GEN_MODEL`: 用于内容生成的模型

## 注意事项

- API调用可能受到网络和服务器状态影响
- 对于某些敏感内容，API可能会返回安全限制错误
- 系统会在API调用失败时使用默认内容
- 纯文本LLM模型响应时间较长，请耐心等待

## 项目结构

项目已经重新组织为模块化结构，详细信息请查看 [STRUCTURE.md](STRUCTURE.md)。

主要模块包括：

- `core/`: 核心功能和业务逻辑
- `ui/`: 用户界面相关组件
- `llm/`: 大语言模型集成
- `resources/`: 资源文件 (assets, llm templates等)
- `tests/`: 测试文件
- `utils/`: 工具类 