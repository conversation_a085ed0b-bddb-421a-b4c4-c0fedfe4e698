"""
测试LLM API实际调用
"""

from resources.prompt_based_generator import call_llm_api, extract_json_from_text, parse_llm_response
import json

def test_api_call():
    """测试LLM API实际调用"""
    print("=== 测试LLM API调用 ===")
    
    # 测试提示词
    test_prompt = """
    你是一位哲学学者，请分析明星崇拜现象的哲学意义，并以JSON格式输出结果，包含以下字段:
    - core_insight: 核心哲学洞察
    - philosopher_perspective: 相关哲学家观点（使用对象数组，每个对象包含name和viewpoint字段）
    - practical_reflection: 对个人生活的启发
    - recommended_reading: 推荐阅读列表（字符串数组）
    - reflection_questions: 思考问题列表（字符串数组）
    """
    
    print("\n使用提示词:")
    print(test_prompt)
    
    # 调用API
    print("\n调用API...")
    response = call_llm_api(test_prompt)
    
    if response:
        # 尝试使用我们的JSON提取和解析函数
        extracted_json = extract_json_from_text(response)
        if extracted_json:
            print("\n成功提取JSON结构:")
            try:
                parsed_json = json.loads(extracted_json)
                pretty_json = json.dumps(parsed_json, ensure_ascii=False, indent=2)
                print(pretty_json)
            except json.JSONDecodeError as e:
                print(f"提取的JSON无法解析: {e}")
                print("原始提取内容:")
                print(extracted_json)
        else:
            print("\n无法提取JSON结构，使用文本处理方法解析:")
            parsed_content = parse_llm_response(response, "philosophy")
            print(json.dumps(parsed_content, ensure_ascii=False, indent=2))
    else:
        print("\nAPI调用失败，未收到响应")
    
    print("\n=== 测试完成 ===")

def test_json_parsing():
    """测试JSON解析功能"""
    print("\n=== 测试JSON解析功能 ===")
    
    # 测试样例1：嵌套结构的JSON
    test_json1 = """```json
    {
      "core_insight": "明星崇拜现象代表了现代社会中的替代性宗教体验",
      "philosopher_perspective": [
        {"name": "尼采", "viewpoint": "对偶像的崇拜是'上帝已死'后的价值重估过程"},
        {"name": "鲍德里亚", "viewpoint": "明星是一种拟像，其影响力来自完美性而非真实性"}
      ],
      "practical_reflection": "应重新审视自己生活中的价值来源...",
      "recommended_reading": ["尼采《欢愉的智慧》", "鲍德里亚《拟像与仿真》", "德波《景观社会》"],
      "reflection_questions": ["我们为何需要偶像？", "如果将追星时间用于自我发展，会有什么改变？"]
    }
    ```"""
    
    # 测试样例2：不完整的JSON（有省略号）
    test_json2 = """
    {
      "core_insight": "明星崇拜现象反映了人类对理想自我的投射...",
      "philosopher_perspective": {
        "尼采": "将崇拜视为力量意志的表现形式",
        "福柯": "名人是规训社会中的话语权力节点..."
      },
      "practical_reflection": "通过反思偶像崇拜，我们可以更好地理解自己的欲望结构",
      "recommended_reading": ["尼采《善恶的彼岸》", "福柯《规训与惩罚》"],
      "reflection_questions": ["我们在偶像身上寻找什么？", "社交媒体如何改变了明星崇拜的形式？"]
    }
    """
    
    # 测试样例3：格式错误的JSON（缺少逗号）
    test_json3 = """
    {
      "core_insight": "明星崇拜是现代社会的象征性消费形式"
      "philosopher_perspective": "齐泽克认为，明星崇拜是意识形态的物质化"
      "practical_reflection": "我们应该审视自己的消费行为背后的欲望结构"
      "recommended_reading": ["齐泽克《意识形态的崇高客体》", "德波《景观社会》"]
      "reflection_questions": ["明星崇拜如何塑造了我们的欲望？", "如何建立更有意义的文化消费方式？"]
    }
    """
    
    print("\n测试样例1 - 嵌套结构JSON:")
    result1 = extract_json_from_text(test_json1)
    print("解析结果: " + ("成功" if result1 else "失败"))
    if result1:
        try:
            parsed = json.loads(result1)
            print(json.dumps(parsed, ensure_ascii=False, indent=2))
        except json.JSONDecodeError as e:
            print(f"解析错误: {e}")
    
    print("\n测试样例2 - 不完整JSON:")
    result2 = extract_json_from_text(test_json2)
    print("解析结果: " + ("成功" if result2 else "失败"))
    if result2:
        try:
            parsed = json.loads(result2)
            print(json.dumps(parsed, ensure_ascii=False, indent=2))
        except json.JSONDecodeError as e:
            print(f"解析错误: {e}")
    
    print("\n测试样例3 - 格式错误JSON:")
    result3 = extract_json_from_text(test_json3)
    print("解析结果: " + ("成功" if result3 else "失败"))
    if result3:
        try:
            parsed = json.loads(result3)
            print(json.dumps(parsed, ensure_ascii=False, indent=2))
        except json.JSONDecodeError as e:
            print(f"解析错误: {e}")
            print("原始提取内容:")
            print(result3)
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_json_parsing()
    test_api_call() 