import os
from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel, QHBoxLayout, QTabWidget
from PyQt5.QtCore import Qt, QPoint
from PyQt5.QtGui import QPainter, QColor, QBrush, QPixmap, QFont
from core.config import ResourceConfig, ResistanceConfig
from core.constant import ResistanceMode
import re

class AdvancedOverlay(QWidget):
    def __init__(self, x, y, w, h, keywords=None, context="", resistance_package=None):
        super().__init__()
        self.keywords = keywords if keywords else []
        self.context = context
        self.resistance_package = resistance_package  # 新增抵抗方案包
        self.content_visible = False
        
        # 获取屏幕尺寸
        screen = QApplication.primaryScreen()
        screen_geometry = screen.availableGeometry()
        
        # 确保位置和尺寸为整数
        x, y, w, h = int(x), int(y), int(w), int(h)
        
        # 打印初始参数
        print(f"AdvancedOverlay初始化: x={x}, y={y}, w={w}, h={h}")
        
        # 调整位置，确保窗口完全在屏幕内
        x = max(10, min(x, screen_geometry.width() - w - 10))
        y = max(10, min(y, screen_geometry.height() - h - 10))
        
        # 调整尺寸，确保不会超出屏幕
        w = min(w, screen_geometry.width() - x - 10)
        h = min(h, screen_geometry.height() - y - 10)
        
        # 记录原始位置和尺寸
        self.original_position = (x, y)  # 记录原始位置
        self.original_size = (w, h)      # 记录原始尺寸
        self.expanded_size = None        # 扩展后的尺寸
        self.expanded_position = None    # 扩展后的位置
        
        # 打印调整后的参数
        print(f"调整后的位置和尺寸: x={x}, y={y}, w={w}, h={h}")
        
        self.initUI(x, y, w, h)
        self.dragging = False
        self.offset = QPoint()
        
    def clean_text(self, text):
        """清理文本中的多余空格和制表符"""
        if not text:
            return ""
        
        # 将制表符转换为空格
        text = text.replace("\t", " ")
        
        # 处理每一行
        lines = text.split("\n")
        cleaned_lines = []
        
        for line in lines:
            # 去除行首行尾的空格
            line = line.strip()
            # 将多个连续空格替换为单个空格
            line = re.sub(r'\s+', ' ', line)
            cleaned_lines.append(line)
        
        # 去除多余的空行（连续的空行只保留一个）
        result_lines = []
        prev_empty = False
        
        for line in cleaned_lines:
            if not line:
                if not prev_empty:
                    result_lines.append(line)
                    prev_empty = True
            else:
                result_lines.append(line)
                prev_empty = False
                
        # 在QLabel中&符号会被当作快捷键，需要替换为&&
        result = "\n".join(result_lines)
        result = result.replace("&", "&&")
        
        # 处理特殊符号
        result = result.replace("<", "&lt;").replace(">", "&gt;")
        
        return result
    
    def remove_title_duplication(self, content_text, title):
        """
        通用的标题重复清理函数
        
        参数:
            content_text: 待清理的内容文本
            title: 可能重复的标题文本
            
        返回:
            清理后的内容文本
        """
        if not content_text or not title:
            return content_text
            
        # 清理空白字符以便更准确地匹配
        cleaned_title = title.strip()
        
        # 检查各种可能的标题格式
        title_patterns = [
            f"【{cleaned_title}】",  # 常见的中文标题格式
            f"《{cleaned_title}》",  # 中文书名号格式
            cleaned_title,           # 直接匹配标题
            f"{cleaned_title}："     # 标题后跟冒号
        ]
        
        # 检查内容开头是否有标题
        for pattern in title_patterns:
            if content_text.startswith(pattern):
                # 移除标题部分
                content_text = content_text[len(pattern):].strip()
                # 如果移除后开头是冒号，也一并移除
                if content_text.startswith("：") or content_text.startswith(":"):
                    content_text = content_text[1:].strip()
                break
        
        # 检查更一般的情况，内容的前几行是否包含标题
        if cleaned_title in content_text[:200]:  # 只在开头部分查找
            lines = content_text.split('\n')
            for i, line in enumerate(lines[:3]):  # 只检查前三行
                if cleaned_title in line:
                    # 移除这一行
                    lines.pop(i)
                    content_text = '\n'.join(lines)
                    break
        
        # 如果开头有换行符，去除
        while content_text.startswith("\n"):
            content_text = content_text[1:].strip()
            
        return content_text
        
    def initUI(self, x, y, w, h):
        # 基本窗口设置
        self.setWindowFlags(
            Qt.FramelessWindowHint | 
            Qt.WindowStaysOnTopHint |
            Qt.Tool
        )
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setGeometry(x, y, w, h)
        
        # 创建主布局
        self.main_layout = QHBoxLayout()
        self.setLayout(self.main_layout)
        
        # 获取抵抗模式
        self.resistance_mode = ResistanceMode.MASK  # 默认为遮罩模式
        if self.resistance_package and "resistance_mode" in self.resistance_package:
            mode_value = self.resistance_package["resistance_mode"]
            for mode in ResistanceMode:
                if mode.value == mode_value:
                    self.resistance_mode = mode
                    break
        
        # 左侧图标
        self.icon_label = QLabel(self)
        self.updateIconImage()
        self.main_layout.addWidget(self.icon_label)
        
        # 右侧内容区域
        self.right_widget = QWidget()
        self.right_layout = QVBoxLayout(self.right_widget)
        
        # 标题标签
        if self.resistance_package:
            title_text = f"污染类型: {self.resistance_package['pollution_name']}"
        else:
            title_text = "关键词: " + ", ".join(self.keywords)
            
        self.title_label = QLabel(self.clean_text(title_text))
        self.title_label.setStyleSheet("""
            color: white;
            font-size: 14px;
            font-weight: bold;
            background-color: rgba(150, 0, 0, 0.8);
            padding: 5px;
            border-radius: 5px;
        """)
        self.title_label.setWordWrap(True)
        self.right_layout.addWidget(self.title_label)
        
        # 如果有抵抗方案包，创建标签页
        if self.resistance_package:
            self.tab_widget = QTabWidget()
            self.tab_widget.setStyleSheet("""
                QTabWidget::pane { 
                    border: 1px solid #444444;
                    background-color: rgba(0, 0, 0, 0.7);
                    border-radius: 5px;
                }
                QTabBar::tab {
                    background-color: rgba(50, 50, 50, 0.8);
                    color: white;
                    padding: 5px;
                    border-top-left-radius: 5px;
                    border-top-right-radius: 5px;
                }
                QTabBar::tab:selected {
                    background-color: rgba(100, 0, 0, 0.8);
                }
            """)
            
            # 创建评论标签页
            comment_tab = QWidget()
            comment_layout = QVBoxLayout(comment_tab)
            comment_label = QLabel(self.clean_text(self.context))
            comment_label.setStyleSheet("color: white; font-size: 12px;")
            comment_label.setWordWrap(True)
            comment_layout.addWidget(comment_label)
            self.tab_widget.addTab(comment_tab, "评论")
            
            # 创建罪名标签页
            if "crime" in self.resistance_package:
                crime_tab = QWidget()
                crime_layout = QVBoxLayout(crime_tab)
                crime_label = QLabel(self.clean_text(self.resistance_package["crime"]))
                crime_label.setStyleSheet("color: white; font-size: 12px;")
                crime_label.setWordWrap(True)
                crime_layout.addWidget(crime_label)
                self.tab_widget.addTab(crime_tab, "罪名")
            
            # 创建文化价值标签页
            if "culture_value" in self.resistance_package:
                value_tab = QWidget()
                value_layout = QVBoxLayout(value_tab)
                value_label = QLabel(self.clean_text(self.resistance_package["culture_value"]))
                value_label.setStyleSheet("color: white; font-size: 12px;")
                value_label.setWordWrap(True)
                value_layout.addWidget(value_label)
                self.tab_widget.addTab(value_tab, "文化价值")
            
            # 检查是否是高级抵抗方案，如果是则显示所有内容类别
            if "method" in self.resistance_package and self.resistance_package["method"].get("type") == "ADVANCED":
                print("检测到高级抵抗方案，创建高级内容标签页")
                
                # 添加主要内容标签页
                if "content" in self.resistance_package:
                    content_tab = QWidget()
                    content_layout = QVBoxLayout(content_tab)
                    
                    # 获取内容文本并清理
                    content_text = self.clean_text(self.resistance_package["content"])
                    
                    # 检查是否有主要内容类型，尝试去除潜在的标题重复
                    if "primary_type" in self.resistance_package and "all_contents" in self.resistance_package:
                        primary_type = self.resistance_package["primary_type"]
                        if primary_type in self.resistance_package["all_contents"]:
                            primary_content = self.resistance_package["all_contents"][primary_type]
                            if "title" in primary_content:
                                title = self.clean_text(primary_content["title"])
                                # 使用通用标题清理函数
                                content_text = self.remove_title_duplication(content_text, title)
                    
                    content_label = QLabel(content_text)
                    content_label.setStyleSheet("color: white; font-size: 12px;")
                    content_label.setWordWrap(True)
                    content_layout.addWidget(content_label)
                    self.tab_widget.addTab(content_tab, "主要内容")
                
                # 如果有all_contents字段，添加所有内容类别的标签页
                if "all_contents" in self.resistance_package:
                    all_contents = self.resistance_package["all_contents"]
                    
                    # 内容类别的中文名称映射
                    category_names = {
                        "symbol_analysis": "符号学解析",
                        "historical_context": "历史脉络",
                        "economic_analysis": "经济利益分析",
                        "reverse_collage": "反向拼贴",
                        "philosophical_card": "哲学反思",
                        "media_literacy": "媒体素养工具",
                        "alternative_reading": "替代性阅读",
                        "cultural_decoding": "文化解码",
                        "social_experiment": "社会实验"
                    }
                    
                    # 为每个内容类别创建标签页
                    for category, content in all_contents.items():
                        # 跳过已经作为主要内容显示的类别
                        if "content" in self.resistance_package and category == self.resistance_package.get("primary_type", ""):
                            continue
                            
                        tab_name = category_names.get(category, category)
                        category_tab = QWidget()
                        category_layout = QVBoxLayout(category_tab)
                        
                        # 创建标题标签
                        if "title" in content:
                            title_label = QLabel(self.clean_text(content["title"]))
                            title_label.setStyleSheet("color: white; font-size: 13px; font-weight: bold;")
                            title_label.setWordWrap(True)
                            category_layout.addWidget(title_label)
                        
                        # 创建内容标签 - 确保内容不会重复显示标题
                        if "content" in content:
                            content_text = self.clean_text(content["content"])
                            
                                                        # 检查内容中是否已包含标题，如果有则避免重复显示
                            if "title" in content:
                                title = self.clean_text(content["title"])
                                # 使用通用标题清理函数
                                content_text = self.remove_title_duplication(content_text, title)
                            
                            content_label = QLabel(content_text)
                            content_label.setStyleSheet("color: white; font-size: 12px;")
                            content_label.setWordWrap(True)
                            category_layout.addWidget(content_label)
                        
                        # 如果有学习资源，添加到标签页
                        if "learning_resources" in content and content["learning_resources"]:
                            resources_label = QLabel("推荐阅读:")
                            resources_label.setStyleSheet("color: white; font-size: 12px; font-weight: bold;")
                            category_layout.addWidget(resources_label)
                            
                            for resource in content["learning_resources"]:
                                resource_label = QLabel(f"• {self.clean_text(resource)}")
                                resource_label.setStyleSheet("color: white; font-size: 12px;")
                                resource_label.setWordWrap(True)
                                category_layout.addWidget(resource_label)
                        
                        self.tab_widget.addTab(category_tab, tab_name)
            else:
                # 非高级抵抗方案，只显示基本内容
                if "content" in self.resistance_package:
                    content_tab = QWidget()
                    content_layout = QVBoxLayout(content_tab)
                    
                    # 获取内容文本并清理
                    content_text = self.clean_text(self.resistance_package["content"])
                    
                    # 避免潜在的标题重复
                    if "method" in self.resistance_package and "name" in self.resistance_package["method"]:
                        method_name = self.resistance_package["method"]["name"]
                        # 使用通用标题清理函数
                        content_text = self.remove_title_duplication(content_text, method_name)
                    
                    content_label = QLabel(content_text)
                    content_label.setStyleSheet("color: white; font-size: 12px;")
                    content_label.setWordWrap(True)
                    content_layout.addWidget(content_label)
                    self.tab_widget.addTab(content_tab, "解构内容")
                
            self.right_layout.addWidget(self.tab_widget)
            
        else:
            # 旧版本的内容显示方式
            self.keywords_label = QLabel("关键词: " + ", ".join(self.keywords))
            self.keywords_label.setStyleSheet("""
                color: white;
                font-size: 12px;
                background-color: rgba(0, 0, 0, 0.7);
                padding: 5px;
                border-radius: 5px;
            """)
            self.keywords_label.setWordWrap(True)
            
            self.context_label = QLabel(self.clean_text(self.context))
            self.context_label.setStyleSheet("""
                color: white;
                font-size: 12px;
                background-color: rgba(0, 0, 0, 0.7);
                padding: 5px;
                border-radius: 5px;
            """)
            self.context_label.setWordWrap(True)
            
            self.right_layout.addWidget(self.keywords_label)
            self.right_layout.addWidget(self.context_label)
        
        # 默认隐藏右侧内容
        self.right_widget.hide()
        self.main_layout.addWidget(self.right_widget)
        
        # 设置布局间距
        self.main_layout.setSpacing(5)
        self.main_layout.setContentsMargins(5, 5, 5, 5)
        
    def updateIconImage(self):
        """根据抵抗方案选择合适的图标"""
        # 默认图标
        icon_path = ResourceConfig.get_resource_path("skull.png")
        
        # 如果有抵抗方案，使用对应的图标
        if self.resistance_package and "method" in self.resistance_package:
            # 打印调试信息
            print("\n=== 抵抗方案信息 ===")
            print(f"抵抗模式: {self.resistance_mode}")
            print(f"污染类型: {self.resistance_package.get('pollution_type', '未知')}")
            if "method" in self.resistance_package:
                method = self.resistance_package["method"]
                print(f"方法名称: {method.get('name', '未知')}")
                print(f"方法类型: {method.get('type', '未知')}")
                if method.get("type", "") == "ADVANCED":
                    print(f"高级抵抗方案 - 内容长度: {len(self.resistance_package.get('content', ''))}")
            
            # 根据抵抗模式和污染类型选择图标
            if self.resistance_mode == ResistanceMode.MASK and "pollution_type" in self.resistance_package:
                # 在遮罩模式下，根据污染类型选择图标
                pollution_type = self.resistance_package["pollution_type"]
                icon_path = ResourceConfig.get_icon_path(pollution_type=pollution_type)
            else:
                # 在其他模式下，使用方法中指定的资源
                method = self.resistance_package["method"]
                if "resource" in method:
                    resource_path = ResourceConfig.get_resource_path(method["resource"])
                    if os.path.exists(resource_path):
                        icon_path = resource_path
        
        print(f"使用图标: {icon_path}")
        
        # 使用图像标签
        icon_pixmap = QPixmap(icon_path)
        if not icon_pixmap.isNull():
            # 调整图标大小，确保适应窗口尺寸
            icon_size = min(self.width()//3, self.height())
            scaled_pixmap = icon_pixmap.scaled(icon_size, icon_size, 
                                            Qt.KeepAspectRatio, 
                                            Qt.SmoothTransformation)
            self.icon_label.setPixmap(scaled_pixmap)
            self.icon_label.setFixedSize(icon_size, icon_size)

    def enterEvent(self, event):
        """当鼠标进入窗口时触发，显示内容区域"""
        # 获取屏幕尺寸
        screen = QApplication.primaryScreen()
        screen_geometry = screen.availableGeometry()
        
        # 保存当前位置作为展开前的位置（如果尚未保存）
        if not self.expanded_position:
            self.expanded_position = (self.x(), self.y())
        
        # 显示右侧内容
        self.right_widget.show()
        
        # 根据内容调整窗口大小
        content_width = self.right_widget.sizeHint().width()
        new_width = self.icon_label.width() + content_width + 30  # 增加边距
        
        # 计算展开后的窗口高度
        expanded_height = max(self.height(), self.right_widget.sizeHint().height() + 30)
        
        # 使用原始位置计算新位置（避免累积位移）
        current_x = self.original_position[0]
        current_y = self.original_position[1]
        
        # 检查是否会超出屏幕底部
        if current_y + expanded_height > screen_geometry.height() - 10:
            # 向上移动窗口
            current_y = max(10, screen_geometry.height() - expanded_height - 10)
            
        # 检查是否会超出屏幕右侧
        if current_x + new_width > screen_geometry.width() - 10:
            # 向左移动窗口
            current_x = max(10, screen_geometry.width() - new_width - 10)
        
        # 记录展开后的位置和尺寸
        self.expanded_position = (current_x, current_y)
        self.expanded_size = (new_width, expanded_height)
        
        # 先移动窗口到正确位置，再调整大小
        self.move(current_x, current_y)
        self.setFixedSize(new_width, expanded_height)
        
    def leaveEvent(self, event):
        """当鼠标离开窗口时触发，隐藏内容区域"""
        # 隐藏右侧内容
        self.right_widget.hide()
        
        # 恢复到原始位置和尺寸
        self.setFixedSize(self.original_size[0], self.original_size[1])
        self.move(self.original_position[0], self.original_position[1])
        
    def mousePressEvent(self, event):
        """鼠标按下事件，开始拖动"""
        if event.button() == Qt.LeftButton:
            self.dragging = True
            self.offset = event.pos()
        
    def mouseMoveEvent(self, event):
        """鼠标移动事件，实现拖动"""
        if self.dragging and event.buttons() & Qt.LeftButton:
            new_pos = self.mapToParent(event.pos() - self.offset)
            
            # 获取屏幕尺寸
            screen = QApplication.primaryScreen()
            screen_geometry = screen.availableGeometry()
            
            # 确保不会拖出屏幕
            new_x = max(0, min(new_pos.x(), screen_geometry.width() - self.width()))
            new_y = max(0, min(new_pos.y(), screen_geometry.height() - self.height()))
            
            # 移动窗口
            self.move(new_x, new_y)
            
            # 更新原始位置，以便在鼠标离开后能回到新位置
            self.original_position = (new_x, new_y)
        
    def mouseReleaseEvent(self, event):
        """鼠标释放事件，结束拖动"""
        if event.button() == Qt.LeftButton:
            self.dragging = False
            
            # 更新原始位置为当前位置
            self.original_position = (self.x(), self.y())
        
    def paintEvent(self, event):
        """绘制窗口背景"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 根据污染类型设置不同的背景颜色
        if self.resistance_package and "pollution_type" in self.resistance_package:
            pollution_type = self.resistance_package["pollution_type"]
            if pollution_type == "CELEBRITY":
                brush = QBrush(QColor(180, 0, 0, 180))  # 红色背景
            elif pollution_type == "ADVERTISEMENT":
                brush = QBrush(QColor(0, 0, 180, 180))  # 蓝色背景
            elif pollution_type == "ENTERTAINMENT":
                brush = QBrush(QColor(180, 90, 0, 180))  # 橙色背景
            elif pollution_type == "HOT_TOPIC":
                brush = QBrush(QColor(130, 0, 130, 180))  # 紫色背景
            else:
                brush = QBrush(QColor(0, 0, 0, 180))  # 默认黑色背景
        else:
            brush = QBrush(QColor(0, 0, 0, 180))  # 默认黑色背景
            
        painter.fillRect(self.rect(), brush)