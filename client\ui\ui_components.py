import os
from PyQt5.QtWidgets import QLabel, QMenu, QAction
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QPixmap, QCursor
from PyQt5.QtWidgets import QApplication

class MouseTipWidget(QLabel):
    """
    鼠标提示小图标
    """
    clicked = pyqtSignal()
    interrupt_clicked = pyqtSignal()  # 中断信号
    mode_switch = pyqtSignal()  # 新增模式切换信号
    
    def __init__(self):
        super().__init__()
        self.is_processing = False
        self.initUI()
        
    def find_resource_path(self, filename):
        """尝试多个可能的路径来查找资源文件"""
        # 可能的路径列表
        base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        possible_paths = [
            os.path.join(base_dir, "resources", "assets", filename),
            os.path.join(base_dir, "resources", filename),
            os.path.join(base_dir, filename),
            os.path.join(os.path.dirname(os.path.abspath(__file__)), "resources", filename),
            os.path.join(os.path.dirname(os.path.abspath(__file__)), filename)
        ]
        
        # 检查每个路径
        for path in possible_paths:
            print(f"尝试路径: {path}")
            if os.path.exists(path):
                print(f"找到资源: {path}")
                return path
                
        # 如果找不到，搜索整个项目目录
        print(f"未找到资源 {filename}，开始搜索整个项目目录...")
        for root, dirs, files in os.walk(base_dir):
            if filename in files:
                path = os.path.join(root, filename)
                print(f"在搜索中找到资源: {path}")
                return path
                
        print(f"警告: 无法找到资源 {filename}")
        return None
        
    def initUI(self):
        # 设置窗口标志
        self.setWindowFlags(
            Qt.FramelessWindowHint |
            Qt.WindowStaysOnTopHint |
            Qt.Tool
        )
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # 加载图片路径 - 尝试多个可能的路径
        self.mouse_path = self.find_resource_path("mouse.png")
        self.eagle_path = self.find_resource_path("神鹰.png")  # 确保这是静态图片
        
        # 调试输出
        print(f"小老鼠图标路径: {self.mouse_path}")
        print(f"神鹰图标路径: {self.eagle_path}")
        print(f"图标文件存在检查 - 小老鼠: {os.path.exists(self.mouse_path) if self.mouse_path else False}, 神鹰: {os.path.exists(self.eagle_path) if self.eagle_path else False}")
        
        self.size = 96  # 图标大小
        
        # 初始显示鼠标图标
        self.show_mouse_icon()
        
        # 移动到屏幕右下角
        screen = QApplication.primaryScreen().geometry()
        self.move(screen.width() - self.size - 20, screen.height() - self.size - 20)
        
        # 设置鼠标样式
        self.setCursor(QCursor(Qt.PointingHandCursor))
        
        # 创建右键菜单
        self.setContextMenuPolicy(Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_context_menu)
    
    def show_context_menu(self, pos):
        """显示右键菜单"""
        if hasattr(self, 'context_menu'):
            self.context_menu.exec_(self.mapToGlobal(pos))
    
    def setContextMenu(self, menu):
        """设置右键菜单"""
        self.context_menu = menu
    
    def show_mouse_icon(self):
        print(f"加载小老鼠图标: {self.mouse_path}")
        if not self.mouse_path or not os.path.exists(self.mouse_path):
            print("警告: 小老鼠图标路径无效，使用替代方案")
            # 使用文本作为替代
            self.setText("🐭")
            self.setStyleSheet("font-size: 48px; background-color: rgba(200, 200, 200, 150); border-radius: 24px;")
            self.setFixedSize(self.size, self.size)
            self.setAlignment(Qt.AlignCenter)
            return
            
        mouse_pixmap = QPixmap(self.mouse_path)
        if not mouse_pixmap.isNull():
            print("小老鼠图标加载成功")
            scaled_pixmap = mouse_pixmap.scaled(self.size, self.size, 
                                              Qt.KeepAspectRatio, 
                                              Qt.SmoothTransformation)
            self.setPixmap(scaled_pixmap)
            self.setFixedSize(self.size, self.size)
        else:
            print("警告: 小老鼠图标加载失败，使用替代方案")
            # 使用文本作为替代
            self.setText("🐭")
            self.setStyleSheet("font-size: 48px; background-color: rgba(200, 200, 200, 150); border-radius: 24px;")
            self.setFixedSize(self.size, self.size)
            self.setAlignment(Qt.AlignCenter)
    
    def show_eagle_icon(self):
        print(f"加载神鹰图标: {self.eagle_path}")
        if not self.eagle_path or not os.path.exists(self.eagle_path):
            print("警告: 神鹰图标路径无效，使用替代方案")
            # 使用文本作为替代
            self.setText("🦅")
            self.setStyleSheet("font-size: 48px; background-color: rgba(200, 0, 0, 150); border-radius: 24px;")
            self.setFixedSize(self.size, self.size)
            self.setAlignment(Qt.AlignCenter)
            return
            
        eagle_pixmap = QPixmap(self.eagle_path)
        if not eagle_pixmap.isNull():
            print("神鹰图标加载成功")
            scaled_pixmap = eagle_pixmap.scaled(self.size, self.size, 
                                              Qt.KeepAspectRatio, 
                                              Qt.SmoothTransformation)
            self.setPixmap(scaled_pixmap)
            self.setFixedSize(self.size, self.size)
        else:
            print("警告: 神鹰图标加载失败，使用替代方案")
            # 使用文本作为替代
            self.setText("🦅")
            self.setStyleSheet("font-size: 48px; background-color: rgba(200, 0, 0, 150); border-radius: 24px;")
            self.setFixedSize(self.size, self.size)
            self.setAlignment(Qt.AlignCenter)
        
    def start_processing(self):
        self.is_processing = True
        self.show_eagle_icon()
        self.setCursor(QCursor(Qt.PointingHandCursor))  # 保持鼠标手型
        
    def stop_processing(self):
        self.is_processing = False
        self.show_mouse_icon()
        self.setCursor(QCursor(Qt.PointingHandCursor))
        
    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            if self.is_processing:
                self.interrupt_clicked.emit()  # 处理状态下点击发出中断信号
            else:
                self.clicked.emit()  # 非处理状态下发出普通点击信号
            
    def enterEvent(self, event):
        if not self.is_processing:
            self.setStyleSheet("background-color: rgba(200, 200, 200, 50); border-radius: 24px;")
        
    def leaveEvent(self, event):
        if not self.is_processing:
            self.setStyleSheet("")