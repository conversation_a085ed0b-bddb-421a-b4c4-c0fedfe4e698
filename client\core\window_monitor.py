import win32gui
import win32process
import psutil
import time
import ctypes
import win32con

# 获取屏幕分辨率
user32 = ctypes.windll.user32
SCREEN_WIDTH = user32.GetSystemMetrics(0)
SCREEN_HEIGHT = user32.GetSystemMetrics(1)

class ApplicationMonitor:
    def __init__(self):
        self.current_app = None
        self.current_window_info = None
        self.last_foreground_window = None
        # 应用模板配置 - 包含特定应用的内容区域和忽略区域的相对坐标
        self.app_templates = {
            "微博(浏览器)": {
                "content_areas": [(0.1, 0.2, 0.9, 0.8)],
                "ignore_areas": [(0, 0, 1.0, 0.15)]
            },
            "微信": {
                "content_areas": [(0.2, 0.1, 0.8, 0.9)],
                "ignore_areas": [(0, 0.9, 1.0, 1.0)]
            },
            "浏览器": {
                "content_areas": [(0.0, 0.15, 1.0, 0.95)],  # 浏览器主内容区域，排除工具栏
                "ignore_areas": [(0, 0, 1.0, 0.1)]  # 浏览器顶部工具栏
            },
            "VS Code": {
                "content_areas": [(0.2, 0.1, 0.8, 0.9)],  # 编辑器主区域
                "ignore_areas": [(0, 0, 0.2, 1.0), (0.8, 0, 1.0, 1.0)]  # 左侧文件树和右侧预览区
            }
        }
        
        # 忽略的应用列表 - 这些应用不会触发应用切换事件
        self.ignored_apps = ["其他应用(python.exe)"]
    
    def check_app_switch(self):
        """检查应用是否切换，并返回当前应用信息"""
        window_info = get_foreground_window_info()
        self.current_window_info = window_info
        app_type = identify_application(window_info)
        
        # 如果当前应用在忽略列表中，则不触发应用切换
        if app_type in self.ignored_apps:
            return False, self.current_app or app_type, None
        
        # 应用切换条件
        if app_type != self.current_app:
            print(f"应用切换: {self.current_app} -> {app_type}")
            self.current_app = app_type
            self.last_foreground_window = window_info
            return True, app_type, self.get_app_template(app_type)
        
        return False, app_type, None
    
    def get_app_template(self, app_type):
        """获取应用的模板配置，结合窗口实际位置"""
        # 获取窗口在屏幕上的实际位置
        if not self.current_window_info:
            # 返回一个完整屏幕的模板
            return {
                "window_rect": (0, 0, SCREEN_WIDTH, SCREEN_HEIGHT),
                "client_rect": (0, 0, SCREEN_WIDTH, SCREEN_HEIGHT),
                "is_maximized": False
            }
        
        # 获取窗口位置信息
        window_rect = self.current_window_info["position"]
        window_x, window_y, window_width, window_height = window_rect
        
        # 检查窗口是否最大化
        hwnd = self.current_window_info["hwnd"]
        is_maximized = bool(win32gui.GetWindowLong(hwnd, win32con.GWL_STYLE) & win32con.WS_MAXIMIZE)
        
        # 获取窗口客户区域（排除标题栏和边框）
        client_rect = win32gui.GetClientRect(hwnd)
        client_width, client_height = client_rect[2], client_rect[3]
        
        # 计算客户区域相对于窗口的偏移
        if not is_maximized:
            # 对于非最大化窗口，需要计算客户区域的实际屏幕坐标
            client_point = win32gui.ClientToScreen(hwnd, (0, 0))
            client_x, client_y = client_point
            border_left = client_x - window_x
            border_top = client_y - window_y
        else:
            # 最大化窗口通常没有可见边框，但可能有标题栏
            border_left = 0
            border_top = window_y
        
        # 计算客户区域的屏幕坐标
        client_screen_x = window_x + border_left
        client_screen_y = window_y + border_top
        client_screen_width = client_width
        client_screen_height = client_height
        
        # 创建一个新的模板，包含实际的屏幕坐标
        actual_template = {
            # 直接使用客户区域作为主要分析区域，不再使用content_areas和ignore_areas
            "window_rect": window_rect,
            "client_rect": (client_screen_x, client_screen_y, client_screen_width, client_screen_height),
            "is_maximized": is_maximized
        }
        
        print(f"应用 {app_type} 的实际区域: {actual_template}")
        return actual_template
    
    def save_window_state(self):
        """保存当前窗口状态"""
        return {
            "current_app": self.current_app,
            "window_info": self.current_window_info,
            "last_window": self.last_foreground_window
        }
    
    def restore_window_state(self, state):
        """恢复之前的窗口状态"""
        if state:
            self.current_app = state.get("current_app", self.current_app)
            self.current_window_info = state.get("window_info", self.current_window_info)
            self.last_foreground_window = state.get("last_window", self.last_foreground_window)
            
            print(f"已恢复窗口状态: {self.current_app}")
            return True
        return False

def get_foreground_window_info():
    """获取当前前台窗口的详细信息"""
    # 获取前台窗口句柄
    hwnd = win32gui.GetForegroundWindow()
    
    # 获取窗口标题
    window_title = win32gui.GetWindowText(hwnd)
    
    # 获取窗口类名
    window_class = win32gui.GetClassName(hwnd)
    
    # 获取窗口位置和大小
    rect = win32gui.GetWindowRect(hwnd)
    x, y, width, height = rect[0], rect[1], rect[2] - rect[0], rect[3] - rect[1]
    
    # 获取进程ID
    _, process_id = win32process.GetWindowThreadProcessId(hwnd)
    
    # 获取进程名称
    try:
        process = psutil.Process(process_id)
        process_name = process.name()
        process_path = process.exe()
    except (psutil.NoSuchProcess, psutil.AccessDenied):
        process_name = "Unknown"
        process_path = "Unknown"
    
    # 检查窗口状态
    window_style = win32gui.GetWindowLong(hwnd, win32con.GWL_STYLE)
    is_visible = bool(window_style & win32con.WS_VISIBLE)
    is_minimized = bool(window_style & win32con.WS_MINIMIZE)
    is_maximized = bool(window_style & win32con.WS_MAXIMIZE)
    
    return {
        "hwnd": hwnd,
        "title": window_title,
        "class": window_class,
        "position": (x, y, width, height),
        "process_id": process_id,
        "process_name": process_name,
        "process_path": process_path,
        "is_visible": is_visible,
        "is_minimized": is_minimized,
        "is_maximized": is_maximized
    }

def monitor_foreground_window():
    """监控前台窗口变化"""
    last_window = None
    
    while True:
        current_window = get_foreground_window_info()
        
        # 检测窗口是否变化
        if last_window is None or current_window["hwnd"] != last_window["hwnd"]:
            print("前台窗口已切换:")
            print(f"标题: {current_window['title']}")
            print(f"进程: {current_window['process_name']}")
            print(f"位置: {current_window['position']}")
            print(f"状态: {'最大化' if current_window['is_maximized'] else '正常'}")
            print("-" * 50)
            
            # 在这里添加您的应用识别逻辑
            identify_application(current_window)
            
            last_window = current_window
        
        time.sleep(0.5)  # 每0.5秒检查一次

def identify_application(window_info):
    """根据窗口信息识别应用类型"""
    title = window_info["title"].lower()
    process = window_info["process_name"].lower()
    
    # 识别常见应用
    if "chrome" in process or "msedge" in process or "firefox" in process:
        app_type = "浏览器"
        # 进一步分析标题可以识别具体网站
        if "微博" in title:
            app_type = "微博(浏览器)"
        elif "twitter" in title or "推特" in title:
            app_type = "Twitter(浏览器)"
    elif "wechat" in process or "微信" in process:
        app_type = "微信"
    elif "dingtalk" in process or "钉钉" in process:
        app_type = "钉钉"
    elif "code" in process and "visual studio code" in title.lower():
        app_type = "VS Code"
    elif "pycharm" in process:
        app_type = "PyCharm"
    else:
        app_type = f"其他应用({process})"
    
    print(f"识别应用类型: {app_type}")
    return app_type