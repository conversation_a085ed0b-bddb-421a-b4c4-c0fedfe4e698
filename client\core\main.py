import mss
import cv2
import numpy as np
from PyQt5.QtWidgets import QA<PERSON><PERSON>, QMainWindow, QMessageBox, QMenu
from PyQt5.QtCore import Qt, QTimer
from llm.demo_llm import analyze_image_stream
import json
import sys

from ui.overlay_manager import OverlayManager
from ui.ui_components import MouseTipWidget
from core.window_monitor import ApplicationMonitor
from core.constant import (
    PromptMode,
    OverlayType,
    DownsampleRate,
    ResistanceMode,
    ResistanceSelectionMode
)
from core.config import (
    SystemConfig,
    ImageConfig,
    PromptConfig,
    OverlayConfig,
    ResistanceConfig
)

class MainWindow(QMainWindow):
    """
    主窗口
    """
    def __init__(self):
        super().__init__()
        self.setGeometry(0, 0, 1, 1)
        self.setWindowFlags(Qt.Tool | Qt.WindowStaysOnTopHint)
        
        # 使用配置中的下采样率
        self.downsample_rate = ImageConfig.get_downsample_rate_value()
        
        # 创建遮罩管理器，设置父对象
        self.overlay_manager = OverlayManager(downsample_rate=self.downsample_rate, parent=self)
        
        # 创建小老鼠图标
        self.mouse_tip = MouseTipWidget()
        self.mouse_tip.clicked.connect(self.analyze_screen)
        self.mouse_tip.interrupt_clicked.connect(self.interrupt_processing)  # 连接中断信号
        self.mouse_tip.mode_switch.connect(self.handle_mode_switch)  # 连接模式切换信号
        self.mouse_tip.show()
        
        # 添加处理状态标志
        self.is_processing = False
        
        # 添加应用监控
        self.app_monitor = ApplicationMonitor()
        
        # 创建一个定时器来检查应用切换
        self.app_check_timer = QTimer()
        self.app_check_timer.timeout.connect(self.check_application)
        self.app_check_timer.start(SystemConfig.APP_CHECK_INTERVAL)  # 设置检查间隔
        
        # 设置提示词模式
        self.prompt_mode = PromptConfig.DEFAULT_MODE
        
        # 设置抵抗模式
        self.resistance_mode = ResistanceConfig.DEFAULT_MODE
        
        # 设置抵抗方案选择模式
        self.selection_mode = ResistanceConfig.DEFAULT_SELECTION_MODE
        
        # 当前应用区域信息
        self.current_app_template = None
        
        # 创建右键菜单
        self.create_context_menu()
        
    def handle_mode_switch(self):
        """处理模式切换"""
        # 切换提示词模式
        mode_name = self.toggle_prompt_mode()
        
        # 同时切换抵抗模式
        resistance_mode_name = self.toggle_resistance_mode()
        
        # 同时切换抵抗方案选择模式
        selection_mode_name = self.toggle_selection_mode()
        
        QMessageBox.information(None, "模式切换", f"已切换到:\n提示词: {mode_name}\n抵抗模式: {resistance_mode_name}\n抵抗方案类型: {selection_mode_name}")

    def interrupt_processing(self):
        """中断当前处理"""
        if self.is_processing:
            self.is_processing = False
            self.mouse_tip.stop_processing()
            print("处理已中断")
    
    def analyze_screen(self):
        if self.is_processing:  # 如果已经在处理中，直接返回
            return
            
        try:
            self.is_processing = True  # 设置处理状态
            # 开始处理时显示神鹰动画
            self.mouse_tip.start_processing()
            QApplication.processEvents()
            
            # 确保抵抗管理器使用当前设置的选择模式
            self.overlay_manager.resistance_manager.set_selection_mode(self.selection_mode)
            
            # 暂停应用切换检测，防止分析过程中因点击导致的焦点变化影响分析
            self.app_check_timer.stop()
            
            # 保存当前窗口状态
            window_state = self.app_monitor.save_window_state()
            
            # 清除上一轮的所有遮罩
            self.overlay_manager.clear_all_overlays()
            
            with mss.mss() as sct:
                # 获取整个屏幕
                full_monitor = sct.monitors[1]
                
                # 如果有应用区域信息，只分析应用区域
                if self.current_app_template and "client_rect" in self.current_app_template:
                    # 使用客户区域作为分析区域
                    client_x, client_y, client_width, client_height = self.current_app_template["client_rect"]
                    
                    # 确保坐标在屏幕范围内
                    client_x = max(0, client_x)
                    client_y = max(0, client_y)
                    client_width = min(full_monitor["width"] - client_x, client_width)
                    client_height = min(full_monitor["height"] - client_y, client_height)
                    
                    # 创建区域监视器
                    area_monitor = {
                        "top": client_y,
                        "left": client_x,
                        "width": client_width,
                        "height": client_height
                    }
                    
                    print(f"分析应用区域: {area_monitor}")
                    img = sct.grab(area_monitor)
                else:
                    # 分析整个屏幕
                    img = sct.grab(full_monitor)
                
                cv_img = cv2.cvtColor(np.array(img), cv2.COLOR_BGRA2BGR)
                
                # 图像下采样
                image_array = cv2.resize(cv_img, None, fx=self.downsample_rate, fy=self.downsample_rate)
                
                # 配置
                api_key = SystemConfig.DEFAULT_API_KEY
                model_name = SystemConfig.DEFAULT_MODEL
                
                # 选择提示词 - 使用增强版还是脏话版
                if self.prompt_mode == PromptMode.ENHANCED:
                    # 使用增强的提示词，根据抵抗模式决定使用哪种
                    from resources.llm.prompt import enhanced_prompt, enhanced_analysis_prompt, dirty_prompt
                    # 如果是高级抵抗内容生成模式，使用enhanced_analysis_prompt
                    if self.resistance_mode == ResistanceMode.PROMPT and self.overlay_manager.resistance_manager.get_generation_mode() == "prompt":
                        prompt = enhanced_analysis_prompt  # 使用增强分析提示词，获取更详细的内容分析
                        print("使用增强分析提示词模板...")
                    else:
                        prompt = enhanced_prompt  # 使用常规增强提示词
                else:
                    from resources.llm.prompt import dirty_prompt
                    prompt = dirty_prompt  # 使用原有的脏话生成助手提示词

                # LLM
                stream = analyze_image_stream(image_array, api_key, model_name=model_name, prompt=prompt)

                print("\n开始接收响应:")
                current_dict = ""
                in_dict = False
                brace_count = 0
                
                # 保存带有详细分析的响应数据列表
                detailed_responses = []
                
                for chunk in stream:
                    if not self.is_processing:  # 检查是否被中断
                        print("\n处理被用户中断")
                        break
                        
                    if chunk.choices[0].delta.content is not None:
                        content = chunk.choices[0].delta.content
                        print(content, end='', flush=True)
                        QApplication.processEvents()
                        
                        for char in content:
                            if not self.is_processing:  # 再次检查是否被中断
                                break
                                
                            if char == '{':
                                if not in_dict:
                                    in_dict = True
                                    current_dict = char
                                else:
                                    current_dict += char
                                brace_count += 1
                            elif char == '}':
                                brace_count -= 1
                                current_dict += char
                                if brace_count == 0 and in_dict:
                                    try:
                                        # 解析响应数据
                                        data = json.loads(current_dict)
                                        
                                        # 保存带有详细分析的响应数据
                                        if isinstance(data, dict) and data.get('flag'):
                                            detailed_responses.append(data)
                                        
                                        # 只处理启用的遮罩类型
                                        overlay_settings = self.overlay_manager.get_overlay_settings()
                                        if isinstance(data, dict) and data.get('flag') and overlay_settings.get(data.get('flag', ''), False):
                                            # 如果是应用区域分析，需要调整坐标
                                            if self.current_app_template and "client_rect" in self.current_app_template:
                                                client_x, client_y = self.current_app_template["client_rect"][0], self.current_app_template["client_rect"][1]
                                                
                                                # 调整position坐标
                                                if "position" in data:
                                                    x1, y1, x2, y2 = data["position"]
                                                    # 确保坐标是整数
                                                    x1, y1, x2, y2 = int(x1), int(y1), int(x2), int(y2)
                                                    
                                                    # 在下采样坐标上添加客户区域偏移
                                                    adjusted_position = [
                                                        x1 + client_x,
                                                        y1 + client_y,
                                                        x2 + client_x,
                                                        y2 + client_y
                                                    ]
                                                    
                                                    print(f"调整坐标: 原始={data['position']}, 偏移=({client_x},{client_y}), 调整后={adjusted_position}")
                                                    data["position"] = adjusted_position
                                                    
                                            self.overlay_manager.process_response(json.dumps(data))
                                        QApplication.processEvents()
                                    except Exception as e:
                                        print(f"\n处理字典时出错: {e}")
                                    current_dict = ""
                                    in_dict = False
                            elif in_dict:
                                current_dict += char
                
                print("\n响应结束")
                
                # 如果是增强分析提示词模式，使用详细分析结果生成抵抗内容
                if (self.prompt_mode == PromptMode.ENHANCED and 
                    self.resistance_mode == ResistanceMode.PROMPT and 
                    self.overlay_manager.resistance_manager.get_generation_mode() == "prompt" and
                    detailed_responses):
                    
                    print("\n使用详细分析结果生成抵抗内容...")
                    # 遍历详细响应数据，为每个区域生成抵抗内容
                    for response_data in detailed_responses:
                        # 检查响应数据是否包含详细分析
                        if response_data.get('flag') == 'Evil':  # 只处理污染信息
                            try:
                                # 传递详细分析结果给抵抗方案生成器
                                if 'detailed_analysis' in response_data:
                                    print(f"使用详细分析结果生成抵抗内容: {response_data['keywords']}")
                                    
                                    # 将detailed_analysis转换为字符串形式，供LLM使用
                                    detailed_analysis_str = json.dumps(response_data['detailed_analysis'], ensure_ascii=False)
                                    
                                    # 将详细分析添加到数据中
                                    response_data['detailed_analysis_str'] = detailed_analysis_str
                                    
                                    # 处理响应生成抵抗内容
                                    self.overlay_manager.process_response(json.dumps(response_data))
                                    QApplication.processEvents()
                            except Exception as e:
                                print(f"\n生成抵抗内容时出错: {e}")
                
        except Exception as e:
            print(f"分析屏幕时出错: {e}")
        finally:
            self.is_processing = False  # 重置处理状态
            self.mouse_tip.stop_processing()
            
            # 恢复之前保存的窗口状态
            if 'window_state' in locals():
                self.app_monitor.restore_window_state(window_state)
            
            # 重新启动应用切换检测
            self.app_check_timer.start(SystemConfig.APP_CHECK_INTERVAL)
            
            QApplication.processEvents()
            
    def toggle_prompt_mode(self):
        """切换提示词模式"""
        if self.prompt_mode == PromptMode.ENHANCED:
            self.prompt_mode = PromptMode.DIRTY
            mode_name = "脏话模式"
        else:
            self.prompt_mode = PromptMode.ENHANCED
            mode_name = "增强模式"
            
        print(f"已切换到{mode_name}")
        return mode_name

    def toggle_resistance_mode(self):
        """切换抵抗模式"""
        # 循环切换抵抗模式
        if self.resistance_mode == ResistanceMode.MASK:
            self.resistance_mode = ResistanceMode.PROMPT
            mode_name = "提示模式"
        elif self.resistance_mode == ResistanceMode.PROMPT:
            self.resistance_mode = ResistanceMode.OBSERVE
            mode_name = "观察模式"
        else:
            self.resistance_mode = ResistanceMode.MASK
            mode_name = "遮罩模式"
            
        # 更新抵抗方案管理器中的模式
        self.overlay_manager.resistance_manager.set_resistance_mode(self.resistance_mode)
        
        print(f"已切换到抵抗{mode_name}")
        return mode_name

    def check_application(self):
        """检查前台应用是否变化"""
        # 如果正在处理，跳过应用检查
        if self.is_processing:
            return
            
        app_switched, current_app, template = self.app_monitor.check_app_switch()
        
        if app_switched:
            print(f"应用已切换到: {current_app}")
            
            # 清除之前的遮罩
            self.overlay_manager.clear_all_overlays()
            
            # 获取应用模板
            self.current_app_template = self.app_monitor.get_app_template(current_app)
            
            # 根据应用类型调整分析策略
            if current_app in self.app_monitor.app_templates:
                # 这里可以根据应用类型调整下采样率
                # 默认使用配置中的下采样率
                self.downsample_rate = ImageConfig.get_downsample_rate_value()
                # 其他策略调整...

    def create_context_menu(self):
        """创建右键菜单"""
        # 设置鼠标提示组件的右键菜单
        self.context_menu = QMenu()
        
        # 添加提示词模式切换菜单项
        prompt_menu = self.context_menu.addMenu("提示词模式")
        enhanced_action = prompt_menu.addAction("增强模式")
        dirty_action = prompt_menu.addAction("脏话模式")
        enhanced_action.triggered.connect(lambda: self.set_prompt_mode(PromptMode.ENHANCED))
        dirty_action.triggered.connect(lambda: self.set_prompt_mode(PromptMode.DIRTY))
        
        # 添加抵抗模式切换菜单项
        resistance_menu = self.context_menu.addMenu("抵抗模式")
        mask_action = resistance_menu.addAction("遮罩模式")
        prompt_action = resistance_menu.addAction("提示模式")
        observe_action = resistance_menu.addAction("观察模式")
        mask_action.triggered.connect(lambda: self.set_resistance_mode(ResistanceMode.MASK))
        prompt_action.triggered.connect(lambda: self.set_resistance_mode(ResistanceMode.PROMPT))
        observe_action.triggered.connect(lambda: self.set_resistance_mode(ResistanceMode.OBSERVE))
        
        # 添加抵抗方案选择模式菜单项
        selection_menu = self.context_menu.addMenu("抵抗方案类型")
        random_action = selection_menu.addAction("随机选择")
        basic_action = selection_menu.addAction("仅基础抵抗")
        advanced_action = selection_menu.addAction("优先高级抵抗")
        creative_action = selection_menu.addAction("优先创意抵抗")
        academic_action = selection_menu.addAction("优先学术解构")
        art_action = selection_menu.addAction("优先艺术武器")
        
        random_action.triggered.connect(lambda: self.set_selection_mode(ResistanceSelectionMode.RANDOM))
        basic_action.triggered.connect(lambda: self.set_selection_mode(ResistanceSelectionMode.BASIC_ONLY))
        advanced_action.triggered.connect(lambda: self.set_selection_mode(ResistanceSelectionMode.ADVANCED_ONLY))
        creative_action.triggered.connect(lambda: self.set_selection_mode(ResistanceSelectionMode.CREATIVE_ONLY))
        academic_action.triggered.connect(lambda: self.set_selection_mode(ResistanceSelectionMode.ACADEMIC_ONLY))
        art_action.triggered.connect(lambda: self.set_selection_mode(ResistanceSelectionMode.ART_ONLY))
        
        # 添加下采样率设置菜单
        downsample_menu = self.context_menu.addMenu("下采样率设置")
        high_quality_action = downsample_menu.addAction("高质量(1.0)")
        balanced_action = downsample_menu.addAction("平衡模式(0.5)")
        performance_action = downsample_menu.addAction("性能模式(0.25)")
        
        high_quality_action.triggered.connect(lambda: self.set_downsample_rate(DownsampleRate.HIGH_QUALITY))
        balanced_action.triggered.connect(lambda: self.set_downsample_rate(DownsampleRate.BALANCED))
        performance_action.triggered.connect(lambda: self.set_downsample_rate(DownsampleRate.PERFORMANCE))
        
        # 添加内容生成模式菜单
        generation_menu = self.context_menu.addMenu("内容生成模式")
        prompt_action = generation_menu.addAction("提示词生成")
        example_action = generation_menu.addAction("预定义例子")
        
        prompt_action.triggered.connect(lambda: self.set_generation_mode("prompt"))
        example_action.triggered.connect(lambda: self.set_generation_mode("example"))
        
        # 添加遮罩位置调整菜单
        position_menu = self.context_menu.addMenu("遮罩位置调整")
        move_right = position_menu.addAction("向右移动(+5)")
        move_left = position_menu.addAction("向左移动(-5)")
        move_up = position_menu.addAction("向上移动(-5)")
        move_down = position_menu.addAction("向下移动(+5)")
        reset_position = position_menu.addAction("重置位置")
        
        move_right.triggered.connect(lambda: self.adjust_overlay_position(5, 0))
        move_left.triggered.connect(lambda: self.adjust_overlay_position(-5, 0))
        move_up.triggered.connect(lambda: self.adjust_overlay_position(0, -5))
        move_down.triggered.connect(lambda: self.adjust_overlay_position(0, 5))
        reset_position.triggered.connect(lambda: self.adjust_overlay_position(0, 0, reset=True))
        
        # 添加测试菜单
        test_menu = self.context_menu.addMenu("测试功能")
        test_advanced_action = test_menu.addAction("测试高级抵抗内容")
        test_advanced_action.triggered.connect(self.test_advanced_resistance)
        
        # 添加退出菜单项
        exit_action = self.context_menu.addAction("退出")
        exit_action.triggered.connect(QApplication.quit)
        
        # 设置鼠标提示组件的右键菜单
        self.mouse_tip.setContextMenu(self.context_menu)
    
    def set_prompt_mode(self, mode):
        """设置提示词模式"""
        if isinstance(mode, PromptMode):
            self.prompt_mode = mode
            mode_name = "增强模式" if mode == PromptMode.ENHANCED else "脏话模式"
            print(f"提示词模式已设置为: {mode_name}")
            QMessageBox.information(None, "模式设置", f"提示词模式已设置为: {mode_name}")
    
    def set_resistance_mode(self, mode):
        """设置抵抗模式"""
        if isinstance(mode, ResistanceMode):
            self.resistance_mode = mode
            self.overlay_manager.resistance_manager.set_resistance_mode(mode)
            mode_name = ResistanceMode(mode.value).name
            print(f"抵抗模式已设置为: {mode_name}")
            QMessageBox.information(None, "模式设置", f"抵抗模式已设置为: {mode_name}")
    
    def set_selection_mode(self, mode):
        """设置抵抗方案选择模式"""
        if isinstance(mode, ResistanceSelectionMode):
            self.selection_mode = mode
            self.overlay_manager.resistance_manager.set_selection_mode(mode)
            mode_name = ResistanceSelectionMode.get_display_name(mode)
            print(f"抵抗方案类型已设置为: {mode_name}")
            QMessageBox.information(None, "模式设置", f"抵抗方案类型已设置为: {mode_name}")

    def toggle_selection_mode(self):
        """循环切换抵抗方案选择模式"""
        # 获取所有选择模式
        modes = list(ResistanceSelectionMode)
        # 找到当前模式的索引
        current_index = modes.index(self.selection_mode)
        # 计算下一个模式的索引
        next_index = (current_index + 1) % len(modes)
        # 设置为下一个模式
        self.set_selection_mode(modes[next_index])
        
        return ResistanceSelectionMode.get_display_name(modes[next_index])

    def test_advanced_resistance(self):
        """测试高级抵抗内容生成"""
        print("\n=== 测试高级抵抗内容生成 ===")
        
        # 确保设置为高级抵抗模式
        self.overlay_manager.resistance_manager.set_selection_mode(ResistanceSelectionMode.ADVANCED_ONLY)
        
        # 创建测试内容数据
        test_data = {
            "keywords": ["测试明星"],
            "type": "明星新闻",
            "comment": "这是一条测试的明星新闻评论"
        }
        
        # 生成抵抗方案包
        resistance_package = self.overlay_manager.resistance_manager.generate_resistance_package(test_data)
        
        # 显示结果
        result_text = f"""
        抵抗方案测试结果:
        
        污染类型: {resistance_package.get('pollution_type', '未知')}
        污染名称: {resistance_package.get('pollution_name', '未知')}
        方案名称: {resistance_package.get('method', {}).get('name', '未知')}
        方案类型: {resistance_package.get('method', {}).get('type', '未知')}
        
        罪名: {resistance_package.get('crime', '未知')}
        
        内容:
        {resistance_package.get('content', '未生成内容')}
        """
        
        QMessageBox.information(None, "高级抵抗内容测试", result_text)

    def adjust_overlay_position(self, x_offset, y_offset, reset=False):
        """调整遮罩位置偏移量"""
        if reset:
            # 重置偏移量
            OverlayConfig.POSITION_OFFSET_X = 0
            OverlayConfig.POSITION_OFFSET_Y = 0
            message = "遮罩位置已重置"
        else:
            # 调整偏移量
            OverlayConfig.POSITION_OFFSET_X += x_offset
            OverlayConfig.POSITION_OFFSET_Y += y_offset
            message = f"遮罩位置已调整: X={OverlayConfig.POSITION_OFFSET_X}, Y={OverlayConfig.POSITION_OFFSET_Y}"
        
        # 清除并重新创建所有遮罩，应用新的偏移量
        self.overlay_manager.clear_all_overlays()
        
        # 显示消息
        QMessageBox.information(None, "位置调整", message)

    def set_downsample_rate(self, rate):
        """设置下采样率"""
        if ImageConfig.set_downsample_rate(rate):
            # 更新当前下采样率
            self.downsample_rate = ImageConfig.get_downsample_rate_value()
            # 更新遮罩管理器的下采样率
            self.overlay_manager.set_downsample_rate(self.downsample_rate)
            # 显示消息
            rate_name = ImageConfig.get_downsample_rate_name()
            QMessageBox.information(None, "下采样率设置", f"下采样率已设置为: {rate_name}")
            print(f"下采样率已设置为: {rate_name}")
            
    def set_generation_mode(self, mode):
        """设置内容生成模式"""
        if self.overlay_manager.resistance_manager.set_generation_mode(mode):
            mode_name = "提示词生成" if mode == "prompt" else "预定义例子"
            QMessageBox.information(None, "生成模式设置", f"内容生成模式已设置为: {mode_name}")
            print(f"内容生成模式已设置为: {mode_name}")

def main():
    try:
        app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = MainWindow()
        main_window.show()

        # 启动应用程序事件循环
        app.exec_()
    except Exception as e:
        print(f"主程序出错: {e}")

if __name__ == '__main__':
    main()