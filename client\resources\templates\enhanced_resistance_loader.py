"""
污染信息抵抗系统 - 高级抵抗方案加载器
用于加载和使用enhanced_resistance_examples.py中的高级抵抗方案
或者使用prompt_based_generator.py动态生成抵抗内容
"""

import sys
import os

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from resources.templates.enhanced_resistance_examples import (
    CELEBRITY_EXAMPLES,
    ENTERTAINMENT_NEWS_EXAMPLES,
    ADVERTISEMENT_EXAMPLES
)

# 导入基于提示词的生成模块
try:
    from resources.llm.prompt_based_generator import generate_prompt_based_content
    PROMPT_BASED_GENERATOR_AVAILABLE = True
except ImportError:
    PROMPT_BASED_GENERATOR_AVAILABLE = False
    print("警告：基于提示词的生成模块不可用，将使用预定义的例子")

# 默认使用的生成模式：example或prompt
DEFAULT_GENERATION_MODE = "prompt" if PROMPT_BASED_GENERATOR_AVAILABLE else "example"

def get_enhanced_resistance_content(pollution_type, resistance_type, context=None):
    """
    获取特定污染类型和抵抗类型的高级抵抗内容
    
    参数:
        pollution_type: 污染类型 ("CELEBRITY", "ENTERTAINMENT", "ADVERTISEMENT")
        resistance_type: 抵抗方式类型 (如 "symbol_analysis", "philosophical_card" 等)
        context: 上下文信息，如明星名称、品牌名称等
    
    返回:
        抵抗内容字典，包含title、content和resources
    """
    example_map = {
        "CELEBRITY": CELEBRITY_EXAMPLES,
        "ENTERTAINMENT": ENTERTAINMENT_NEWS_EXAMPLES,
        "ADVERTISEMENT": ADVERTISEMENT_EXAMPLES
    }
    
    if pollution_type not in example_map or resistance_type not in example_map[pollution_type]:
        return {
            "title": "抵抗内容不可用",
            "content": "未找到对应的高级抵抗内容",
            "resources": []
        }
    
    resistance_content = example_map[pollution_type][resistance_type]
    
    # 如果有上下文，可以在这里处理内容的替换
    # 例如将content中的占位符替换为实际值
    
    return resistance_content

def get_advanced_resistance_content(method_id, pollution_type, keywords=None, generation_mode=None, pollution_data=None):
    """
    获取高级抵抗方案内容
    
    参数:
        method_id: 抵抗方法ID
        pollution_type: 污染类型
        keywords: 关键词列表
        generation_mode: 生成模式 "example"(使用预定义例子) 或 "prompt"(使用提示词生成)
        pollution_data: 包含更详细分析结果的污染数据(可选)
    
    返回:
        包含所有高级抵抗内容的字典
    """
    print(f"\n=== 高级抵抗内容生成 ===")
    print(f"方法ID: {method_id}")
    print(f"污染类型: {pollution_type}")
    print(f"关键词: {keywords}")
    
    # 如果未指定生成模式，使用默认模式
    if generation_mode is None:
        generation_mode = DEFAULT_GENERATION_MODE
    
    print(f"生成模式: {generation_mode}")
    
    # 根据method_id映射到对应的抵抗类型和内容类型
    method_to_type_map = {
        "CELEBRITY_SYMBOL_ANALYSIS": ("symbol_analysis", "symbol"),
        "CELEBRITY_ECONOMIC_ANALYSIS": ("economic_analysis", "economics"),
        "CELEBRITY_PHILOSOPHICAL_REFLECTION": ("philosophical_card", "philosophy"),
        "ENTERTAINMENT_NEWS_DECODING": ("symbol_analysis", "symbol"),
        "AD_DESIRE_ENGINEERING": ("symbol_analysis", "symbol"),
        "CONSUMPTION_PHILOSOPHY": ("philosophical_card", "philosophy")
    }
    
    # 映射污染类型
    pollution_map = {
        "CELEBRITY": "CELEBRITY",
        "ENTERTAINMENT": "ENTERTAINMENT", 
        "ADVERTISEMENT": "ADVERTISEMENT",
        "LOW_QUALITY": "ENTERTAINMENT",
        "HOT_TOPIC": "ENTERTAINMENT"
    }
    
    if method_id not in method_to_type_map:
        print(f"错误：未找到方法ID对应的抵抗类型")
        return {
            "main_content": "未找到对应的高级抵抗内容",
            "all_contents": {}
        }
    
    resistance_type, content_type = method_to_type_map[method_id]
    mapped_pollution_type = pollution_map.get(pollution_type, "CELEBRITY")
    
    print(f"映射后的污染类型: {mapped_pollution_type}")
    print(f"抵抗类型: {resistance_type}")
    print(f"内容类型: {content_type}")
    
    # 如果没有传递pollution_data，构建基本的污染信息数据
    if pollution_data is None:
        pollution_data = {
            "keywords": keywords or [],
            "type": mapped_pollution_type,
            "comment": ""  # 可以在这里添加更多上下文信息
        }
    else:
        # 确保污染数据包含必要字段
        if "keywords" not in pollution_data:
            pollution_data["keywords"] = keywords or []
        if "type" not in pollution_data:
            pollution_data["type"] = mapped_pollution_type
    
    # 检查是否有详细分析数据
    has_detailed_analysis = "detailed_analysis" in pollution_data
    if has_detailed_analysis:
        print("检测到详细分析数据，将用于增强抵抗内容生成")
    
    # 根据生成模式选择生成方法
    if generation_mode == "prompt" and PROMPT_BASED_GENERATOR_AVAILABLE:
        try:
            print("使用基于提示词的生成模式...")
            # 根据content_type调用对应的生成函数
            if content_type == "philosophy":
                main_content = generate_prompt_based_content("philosophy", pollution_data)
            elif content_type == "economics":
                main_content = generate_prompt_based_content("economics", pollution_data)
            elif content_type == "symbol":
                # 符号学分析可以使用不同的生成器，这里使用历史分析作为示例
                main_content = generate_prompt_based_content("historical", pollution_data)
            else:
                # 默认使用哲学分析
                main_content = generate_prompt_based_content("philosophy", pollution_data)
                
            # 同时获取其他类型的内容，构建完整的抵抗内容包
            all_contents = {}
            
            # 添加主要内容
            all_contents[resistance_type] = main_content
            
            # 添加其他补充内容
            if content_type != "philosophy":
                all_contents["philosophical_card"] = generate_prompt_based_content("philosophy", pollution_data)
            if content_type != "economics":
                all_contents["economic_analysis"] = generate_prompt_based_content("economics", pollution_data)
            if content_type != "historical":
                all_contents["historical_context"] = generate_prompt_based_content("historical", pollution_data)
            
            result = {
                "main_content": format_enhanced_resistance_for_display_from_content(main_content),
                "all_contents": all_contents
            }
            
            print(f"生成内容类别数量: {len(all_contents)}")
            return result
            
        except Exception as e:
            print(f"基于提示词生成失败，回退到预定义例子: {e}")
            # 如果提示词生成失败，回退到例子模式
    
    # 使用预定义例子模式
    print("使用预定义例子模式...")
    
    # 构建上下文
    context = None
    if keywords and len(keywords) > 0:
        context = {"name": keywords[0], "brand": keywords[0], "topic": keywords[0]}
    
    # 获取所有高级抵抗内容
    all_contents = get_all_enhanced_contents(mapped_pollution_type, resistance_type, context)
    
    # 格式化主要内容用于显示
    main_content = format_enhanced_resistance_for_display(mapped_pollution_type, resistance_type, context)
    
    result = {
        "main_content": main_content,
        "all_contents": all_contents
    }
    
    print(f"生成内容类别数量: {len(all_contents)}")
    return result

def get_all_enhanced_contents(pollution_type, primary_type, context=None):
    """
    获取特定污染类型的所有高级抵抗内容
    
    参数:
        pollution_type: 污染类型 ("CELEBRITY", "ENTERTAINMENT", "ADVERTISEMENT")
        primary_type: 主要抵抗类型，用于确定主要内容
        context: 上下文信息
    
    返回:
        包含所有高级抵抗内容的字典
    """
    example_map = {
        "CELEBRITY": CELEBRITY_EXAMPLES,
        "ENTERTAINMENT": ENTERTAINMENT_NEWS_EXAMPLES,
        "ADVERTISEMENT": ADVERTISEMENT_EXAMPLES
    }
    
    if pollution_type not in example_map:
        return {}
    
    # 获取该污染类型下的所有抵抗内容
    contents = {}
    
    # 预定义的内容类别顺序
    content_categories = [
        "symbol_analysis",          # 深层解构维度
        "historical_context",       # 历史脉络
        "economic_analysis",        # 经济利益分析
        "reverse_collage",          # 抵抗形式
        "philosophical_card",       # 哲学反思
        "media_literacy",           # 教育性抵抗资源
        "alternative_reading",      # 替代性阅读
        "cultural_decoding",        # 交互式抵抗设计
        "social_experiment"         # 社会实验
    ]
    
    # 首先添加主要类型
    if primary_type in example_map[pollution_type]:
        contents[primary_type] = example_map[pollution_type][primary_type]
    
    # 然后按顺序添加其他类型
    for category in content_categories:
        if category != primary_type and category in example_map[pollution_type]:
            contents[category] = example_map[pollution_type][category]
    
    return contents

def get_enhanced_resistance_types(pollution_type):
    """
    获取特定污染类型可用的高级抵抗类型列表
    
    参数:
        pollution_type: 污染类型 ("CELEBRITY", "ENTERTAINMENT", "ADVERTISEMENT")
    
    返回:
        可用的抵抗类型列表
    """
    example_map = {
        "CELEBRITY": CELEBRITY_EXAMPLES,
        "ENTERTAINMENT": ENTERTAINMENT_NEWS_EXAMPLES,
        "ADVERTISEMENT": ADVERTISEMENT_EXAMPLES
    }
    
    if pollution_type not in example_map:
        return []
    
    return list(example_map[pollution_type].keys())

def get_all_enhanced_resistance_examples():
    """
    获取所有高级抵抗方案示例
    
    返回:
        所有抵抗方案示例的字典
    """
    return {
        "CELEBRITY": CELEBRITY_EXAMPLES,
        "ENTERTAINMENT": ENTERTAINMENT_NEWS_EXAMPLES,
        "ADVERTISEMENT": ADVERTISEMENT_EXAMPLES
    }

def get_learning_resources(pollution_type, resistance_type):
    """
    获取特定污染类型和抵抗类型的学习资源
    
    参数:
        pollution_type: 污染类型 ("CELEBRITY", "ENTERTAINMENT", "ADVERTISEMENT")
        resistance_type: 抵抗方式类型
    
    返回:
        学习资源列表
    """
    resistance_content = get_enhanced_resistance_content(pollution_type, resistance_type)
    if "learning_resources" in resistance_content:
        return resistance_content["learning_resources"]
    return []

def format_enhanced_resistance_for_display_from_content(content):
    """
    从内容字典直接格式化内容用于显示
    
    参数:
        content: 内容字典，包含title、content和resources
        
    返回:
        格式化后的显示文本
    """
    if not content:
        return "未能生成抵抗内容"
    
    # 直接使用内容，不重复添加标题
    formatted_text = content.get('content', '')
    
    resources = content.get('resources', [])
    if resources:
        formatted_text += "\n\n推荐阅读:\n"
        for resource in resources:
            formatted_text += f"- {resource}\n"
    
    return formatted_text

def format_enhanced_resistance_for_display(pollution_type, resistance_type, context=None):
    """
    格式化高级抵抗内容用于显示
    
    参数:
        pollution_type: 污染类型
        resistance_type: 抵抗类型
        context: 上下文信息
    
    返回:
        格式化后的显示文本
    """
    # 直接从示例库中获取内容
    example_map = {
        "CELEBRITY": CELEBRITY_EXAMPLES,
        "ENTERTAINMENT": ENTERTAINMENT_NEWS_EXAMPLES,
        "ADVERTISEMENT": ADVERTISEMENT_EXAMPLES
    }
    
    if pollution_type not in example_map:
        return "未找到对应的高级抵抗内容"
    
    if resistance_type not in example_map[pollution_type]:
        # 尝试使用symbol_analysis作为备选
        if "symbol_analysis" in example_map[pollution_type]:
            resistance_type = "symbol_analysis"
        else:
            return "未找到对应的高级抵抗内容"
    
    resistance = example_map[pollution_type][resistance_type]
    
    # 直接使用内容，不重复添加标题
    formatted_text = resistance['content']
    
    if "learning_resources" in resistance and resistance["learning_resources"]:
        formatted_text += "\n\n推荐阅读:\n"
        for resource in resistance["learning_resources"]:
            formatted_text += f"- {resource}\n"
    
    return formatted_text 