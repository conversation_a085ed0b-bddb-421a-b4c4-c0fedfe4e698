"""
测试两步法调用流程
模拟VLM图像分析和LLM内容生成的协同工作
"""

import json
from config import SystemConfig
from resources.prompt_based_generator import generate_prompt_based_content

def test_two_step_flow():
    """测试两步法调用流程"""
    print("=== 测试两步法调用流程 ===")
    
    # 步骤1：模拟VLM分析结果，包含详细的分析数据
    # 实际系统中，这些数据来自于VLM对图像的分析
    vlm_analysis_result = {
        "position": [985, 320, 1647, 595],
        "flag": "Evil",
        "type": "娱乐新闻",
        "keywords": ["孙俪", "唇下痣", "疤痕", "礼服"],
        "comment": "又是一则无意义的明星外貌新闻，这种内容只会消耗社会注意力资源。",
        "detailed_analysis": {
            "content_summary": "这是一则关于女演员孙俪参加活动时礼服和妆容的娱乐报道，特别关注了她唇下痣的变化。文章将这种细微的外貌变化作为重点，试图吸引读者关注明星的外貌细节。这类内容属于典型的明星日常生活报道，不包含实质性信息。",
            "potential_impact": "这类内容对受众的影响主要表现为将注意力引导至名人外表和生活琐事，分散人们对更重要社会议题的关注。长期沉浸此类内容可能强化以外貌为中心的价值观，助长名人崇拜文化。",
            "value_assessment": "该信息实际价值极低，不包含任何对受众有启发、教育或实用价值的内容。属于典型的注意力消耗型内容，消费者从中获取的只是短暂的娱乐感。",
            "attention_mechanism": "通过利用人们对名人的好奇心和对外貌变化的敏感性来吸引注意力。标题使用了'疑似'等词汇制造悬念，配图特意突出面部特征，以引发读者猜测和讨论。",
            "context": "这类内容通常出现在娱乐资讯平台和社交媒体，是明星经济产业链的一部分，为相关平台带来流量和广告收益。"
        }
    }
    
    # 将detailed_analysis转换为字符串，模拟实际环境中的数据传递
    vlm_analysis_result["detailed_analysis_str"] = json.dumps(vlm_analysis_result["detailed_analysis"], ensure_ascii=False)
    
    # 打印VLM分析结果
    print("\n第一步：VLM图像分析结果")
    print(f"关键词: {vlm_analysis_result['keywords']}")
    print(f"内容类型: {vlm_analysis_result['type']}")
    print(f"简短评论: {vlm_analysis_result['comment']}")
    print("详细分析: 已生成")
    
    # 步骤2：使用VLM分析结果生成抵抗内容
    # 准备污染数据
    pollution_data = {
        "keywords": vlm_analysis_result["keywords"],
        "type": "CELEBRITY",  # 手动映射类型
        "comment": vlm_analysis_result["comment"],
        "detailed_analysis": vlm_analysis_result["detailed_analysis_str"]
    }
    
    # 生成哲学抵抗内容
    print("\n第二步：LLM生成抵抗内容")
    philosophy_content = generate_prompt_based_content("philosophy", pollution_data)
    
    # 打印生成的内容
    print("\n生成的哲学抵抗内容:")
    print(f"标题: {philosophy_content['title']}")
    print(f"内容: {philosophy_content['content'][:200]}...")  # 只显示前200字符
    print(f"资源数量: {len(philosophy_content['resources'])}")
    
    # 生成经济学抵抗内容
    economics_content = generate_prompt_based_content("economics", pollution_data)
    
    # 打印生成的内容
    print("\n生成的经济学抵抗内容:")
    print(f"标题: {economics_content['title']}")
    print(f"内容: {economics_content['content'][:200]}...")  # 只显示前200字符
    print(f"资源数量: {len(economics_content['resources'])}")
    
    print("\n=== 测试完成 ===")
    
if __name__ == "__main__":
    test_two_step_flow() 