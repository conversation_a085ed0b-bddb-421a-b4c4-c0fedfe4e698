from resources.prompt_based_generator import PHILOSOPHY_ENGINE_PROMPT, ECONOMICS_ENGINE_PROMPT, CREATIVE_ENGINE_PROMPT, HISTORICAL_ENGINE_PROMPT, INTEGRATION_ENGINE_PROMPT

def test_format():
    """Test the format method on all prompt templates"""
    test_data = {
        "pollution_content": "测试内容",
        "pollution_type": "测试类型",
        "keywords": "测试关键词",
        "philosophy_result": "测试哲学结果"
    }
    
    try:
        print("Testing PHILOSOPHY_ENGINE_PROMPT...")
        philosophy_prompt = PHILOSOPHY_ENGINE_PROMPT.format(
            pollution_content=test_data["pollution_content"],
            pollution_type=test_data["pollution_type"],
            keywords=test_data["keywords"]
        )
        print("PHILOSOPHY_ENGINE_PROMPT formatting successful!")
        
        print("\nTesting ECONOMICS_ENGINE_PROMPT...")
        economics_prompt = ECONOMICS_ENGINE_PROMPT.format(
            pollution_content=test_data["pollution_content"],
            pollution_type=test_data["pollution_type"],
            keywords=test_data["keywords"]
        )
        print("ECONOMICS_ENGINE_PROMPT formatting successful!")
        
        print("\nTesting CREATIVE_ENGINE_PROMPT...")
        creative_prompt = CREATIVE_ENGINE_PROMPT.format(
            pollution_content=test_data["pollution_content"],
            pollution_type=test_data["pollution_type"],
            keywords=test_data["keywords"]
        )
        print("CREATIVE_ENGINE_PROMPT formatting successful!")
        
        print("\nTesting HISTORICAL_ENGINE_PROMPT...")
        historical_prompt = HISTORICAL_ENGINE_PROMPT.format(
            pollution_content=test_data["pollution_content"],
            pollution_type=test_data["pollution_type"],
            keywords=test_data["keywords"]
        )
        print("HISTORICAL_ENGINE_PROMPT formatting successful!")
        
        print("\nTesting INTEGRATION_ENGINE_PROMPT...")
        integration_prompt = INTEGRATION_ENGINE_PROMPT.format(
            philosophy_result=test_data["philosophy_result"],
            pollution_content=test_data["pollution_content"],
            pollution_type=test_data["pollution_type"],
            keywords=test_data["keywords"]
        )
        print("INTEGRATION_ENGINE_PROMPT formatting successful!")
        
        print("\nAll prompt templates formatted successfully!")
        
    except KeyError as e:
        print(f"KeyError: {e}")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_format() 