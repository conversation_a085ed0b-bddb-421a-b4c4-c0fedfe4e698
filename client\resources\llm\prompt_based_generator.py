"""
污染信息抵抗系统 - 基于提示词的抵抗内容生成器
使用提示词工程来动态生成抵抗内容，而不是依赖预定义的例子
"""

import os
import json
import re
from config import SystemConfig
import requests
from openai import OpenAI
import time

# 哲学分析引擎提示词模板
PHILOSOPHY_ENGINE_PROMPT = """
你是一位哲学学者，专精于将日常现象与哲学理论联系。

任务：分析以下污染信息的哲学意义
污染内容：{pollution_content}
污染类型：{pollution_type}
关键词：{keywords}

请从以下哲学角度分析：

1. **存在论维度**：这种现象反映了怎样的存在状态？
2. **认识论维度**：它如何影响人们的认知方式？
3. **价值论维度**：体现了什么样的价值观冲突？
4. **美学维度**：从审美角度如何理解这种现象？

请选择最相关的哲学家观点进行分析：
- 从尼采、叔本华、萨特的视角
- 从阿多诺、本雅明、鲍德里亚的媒体批判视角
- 从海德格尔、德勒兹、阿甘本的复杂理论视角

输出格式：
{{
  "core_insight": "核心哲学洞察",
  "philosopher_perspective": "相关哲学家观点",
  "practical_reflection": "对个人生活的启发",
  "recommended_reading": ["推荐阅读1", "推荐阅读2"],
  "reflection_questions": ["思考问题1", "思考问题2"]
}}
"""

# 经济分析引擎提示词模板
ECONOMICS_ENGINE_PROMPT = """
你是一位政治经济学专家，擅长分析文化现象背后的经济逻辑。

分析对象：{pollution_content}
污染类型：{pollution_type}
关键词：{keywords}

请从以下角度进行经济分析：

1. **价值链分析**：这个内容的经济价值如何产生和分配？
2. **注意力经济**：如何将用户注意力转化为经济收益？
3. **市场机制**：供需关系是怎样的？谁是真正的消费者？
4. **外部性分析**：对社会产生了哪些正面/负面的外部效应？
5. **权力结构**：资本、平台、内容方、用户之间的权力关系如何？

根据分析深度，引入相关经济学理论：
- 马克思主义经济学
- 注意力经济理论
- 符号经济学

JSON输出：
{{
  "value_chain": "价值链分析",
  "attention_economics": "注意力经济分析", 
  "market_dynamics": "市场机制分析",
  "power_structure": "权力结构分析",
  "social_cost": "社会成本评估",
  "data_evidence": ["支撑数据1", "支撑数据2"]
}}
"""

# 创意转换引擎提示词模板
CREATIVE_ENGINE_PROMPT = """
你是一位概念艺术家和创意写作专家，善于将低俗内容转化为高雅的艺术表达。

转换对象：{pollution_content}
污染类型：{pollution_type}
关键词：{keywords}

创意转换任务：
1. **诗歌改写**：将内容核心转化为讽刺诗歌（选择合适的诗歌形式）
2. **视觉概念**：设计一个艺术装置或行为艺术概念
3. **叙事重构**：将事件改写为寓言、神话或科幻故事
4. **符号替换**：用高雅文化符号替代低俗符号

创意方向参考：
- 超现实主义：像达利、马格利特那样的荒诞转换
- 波普艺术：像沃霍尔那样的商业符号解构
- 概念艺术：像杜尚那样的现成品再语境化
- 行为艺术：像阿布拉莫维奇那样的身体政治

输出格式：
{{
  "poetry_version": "诗歌版本",
  "visual_concept": "视觉艺术概念",
  "narrative_rewrite": "叙事重构",
  "artistic_statement": "艺术阐述",
  "creation_method": "创作方法说明",
  "cultural_elevation": "文化提升说明"
}}
"""

# 历史脉络引擎提示词模板
HISTORICAL_ENGINE_PROMPT = """
你是一位文化史学者，专门研究大众文化现象的历史脉络。

研究对象：{pollution_content}
污染类型：{pollution_type}
关键词：{keywords}

历史分析任务：
1. **历史比较**：在人类文化史上找到相似现象
2. **演变轨迹**：这种现象是如何发展到今天的？
3. **周期性分析**：是否存在历史周期性规律？
4. **技术影响**：媒介技术变化如何影响这种现象？
5. **未来预测**：基于历史规律，预测可能的发展方向

历史参照系选择：
- 古代：古罗马的面包与马戏、中国古代的戏曲娱乐
- 近代：19世纪的报纸煽情主义、早期好莱坞明星制度  
- 现代：电视时代的大众文化、互联网早期的现象级事件

JSON输出：
{{
  "historical_parallels": ["历史类似现象1", "历史类似现象2"],
  "evolution_timeline": "演变时间线",
  "cyclical_patterns": "周期性规律",
  "technological_impact": "技术影响分析",
  "future_projection": "未来发展预测",
  "lessons_learned": "历史教训"
}}
"""

# 内容整合引擎提示词模板
INTEGRATION_ENGINE_PROMPT = """
你是一位教育心理学专家，负责将专业分析整合为用户友好的内容。

输入材料：
- 哲学分析：{philosophy_result}
- 污染内容：{pollution_content}
- 污染类型：{pollution_type}
- 关键词：{keywords}

整合任务：
1. **内容筛选**：选择最相关的分析角度
2. **难度调整**：将复杂理论转化为可理解的表达
3. **连贯性**：确保内容逻辑统一
4. **实用性**：提供具体的行动建议
5. **个性化**：根据内容特点调整表达方式

输出要求：
- 主要内容：1-2个最相关的分析维度（300-500字）
- 延伸阅读：3-5个推荐资源，按难度排序
- 行动建议：具体可执行的抵抗行动
- 反思问题：引导用户深度思考的问题

JSON输出：
{{
  "main_content": "整合后的主要内容",
  "extended_readings": ["资源1", "资源2", "资源3"],
  "action_suggestions": ["行动1", "行动2"],
  "reflection_questions": ["问题1", "问题2"],
  "complexity_level": "内容复杂度评估"
}}
"""

def generate_prompt_based_content(content_type, pollution_data):
    """
    基于提示词生成动态抵抗内容
    
    参数:
        content_type: 内容类型 ("philosophy", "economics", "creative", "historical")
        pollution_data: 污染信息数据字典，包含keywords、type等信息
    
    返回:
        生成的内容字典
    """
    import random
    import time
    
    # 生成缓存键，用于缓存相同污染类型和内容的结果
    # 添加一个随机因素和时间戳，确保相似内容也能生成不同的结果
    random_factor = random.randint(1, 1000)
    timestamp = int(time.time())
    cache_key = f"{content_type}_{pollution_data.get('type', 'unknown')}_{','.join(pollution_data.get('keywords', []))}_{random_factor}_{timestamp}"
    
    # 检查是否有缓存结果（此功能已禁用，见get_cached_content函数）
    cached_content = get_cached_content(cache_key)
    if cached_content:
        print(f"使用缓存的{content_type}类型内容")
        return cached_content
    
    # 提取污染信息
    keywords = pollution_data.get("keywords", [])
    pollution_type = pollution_data.get("type", "未知类型")
    comment = pollution_data.get("comment", "")
    raw_description = pollution_data.get("raw_description", "")
    
    # 如果有VLM生成的更详细描述，优先使用
    detailed_analysis = pollution_data.get("detailed_analysis", None)
    
    # 构建污染内容描述 - 简化描述以减少token使用
    if detailed_analysis:
        # 使用详细分析结果，但限制长度
        pollution_content = detailed_analysis[:500]  # 限制长度，避免过长提示词
        if len(detailed_analysis) > 500:
            pollution_content += "..."
    else:
        # 使用基本信息构建描述
        pollution_content = f"类型：{pollution_type}，关键词：{', '.join(keywords)}，内容：{comment}"
        
        # 如果有原始描述，添加到内容中
        if raw_description:
            # 截断过长的原始描述
            if len(raw_description) > 200:
                raw_description = raw_description[:200] + "..."
            pollution_content += f"，原始描述：{raw_description}"
    
    # 选择适当的提示词模板 - 简化版本以减少生成时间
    if content_type == "philosophy":
        prompt_template = """
        你是一位哲学学者，请分析以下污染信息的哲学意义，并以JSON格式输出。
        
        污染内容：{pollution_content}
        污染类型：{pollution_type}
        关键词：{keywords}
        
        请简洁地从哲学角度分析，并输出以下JSON格式：
        {{
          "core_insight": "核心哲学洞察",
          "philosopher_perspective": "相关哲学家观点",
          "practical_reflection": "对个人生活的启发",
          "recommended_reading": ["推荐阅读1", "推荐阅读2"],
          "reflection_questions": ["思考问题1", "思考问题2"]
        }}
        """
    elif content_type == "economics":
        prompt_template = """
        你是一位政治经济学专家，请简洁分析以下污染信息背后的经济逻辑，并以JSON格式输出。
        
        污染内容：{pollution_content}
        污染类型：{pollution_type}
        关键词：{keywords}
        
        请输出以下JSON格式：
        {{
          "value_chain": "价值链分析",
          "attention_economics": "注意力经济分析", 
          "power_structure": "权力结构分析",
          "social_cost": "社会成本评估"
        }}
        """
    elif content_type == "creative":
        prompt_template = """
        你是一位概念艺术家和创意写作专家，请将以下污染内容转化为高雅的艺术表达，并以JSON格式输出。
        
        污染内容：{pollution_content}
        污染类型：{pollution_type}
        关键词：{keywords}
        
        请输出以下JSON格式：
        {{
          "poetry_version": "诗歌版本",
          "visual_concept": "视觉艺术概念",
          "narrative_rewrite": "叙事重构",
          "artistic_statement": "艺术阐述"
        }}
        """
    elif content_type == "historical":
        prompt_template = """
        你是一位文化史学者，请分析以下污染信息的历史脉络，并以JSON格式输出。
        
        污染内容：{pollution_content}
        污染类型：{pollution_type}
        关键词：{keywords}
        
        请输出以下JSON格式：
        {{
          "historical_parallels": ["历史类似现象1", "历史类似现象2"],
          "evolution_timeline": "演变时间线",
          "future_projection": "未来发展预测",
          "lessons_learned": "历史教训"
        }}
        """
    else:
        # 未知类型，使用默认内容
        return create_default_content(content_type, pollution_data)
    
    # 填充提示词模板
    prompt = prompt_template.format(
        pollution_content=pollution_content,
        pollution_type=pollution_type,
        keywords=", ".join(keywords)
    )
    
    # 调用API生成内容
    generated_content = call_llm_api(prompt)
    
    # 如果生成失败，返回默认内容
    if not generated_content:
        return create_default_content(content_type, pollution_data)
    
    # 解析生成的内容并进行后处理
    parsed_content = parse_llm_response(generated_content, content_type)
    
    # 格式化内容
    formatted_content = format_content_for_display(parsed_content, content_type)
    
    # 缓存结果
    cache_content(cache_key, formatted_content)
    
    # 返回格式化后的内容
    return formatted_content

def call_llm_api(prompt):
    """
    调用大语言模型API生成内容，使用流式输出
    
    参数:
        prompt: 提示词
        
    返回:
        生成的内容文本
    """
    # 获取API配置
    api_key = SystemConfig.DEFAULT_API_KEY
    model = SystemConfig.CONTENT_GEN_MODEL  # 使用专门的内容生成模型
    
    # 如果没有API密钥，返回空内容
    if not api_key:
        print("错误：未配置API密钥")
        return None
    
    try:
        print(f"调用LLM API生成内容... 使用模型: {model}")
        print("正在等待响应...")
        
        # 初始化OpenAI客户端
        client = OpenAI(
            api_key=api_key,
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
            timeout=60.0  # 设置60秒超时
        )
        
        # 构建消息
        messages = [
            {
                "role": "user",
                "content": prompt
            }
        ]
        
        # 调用API - 使用流式响应
        stream = client.chat.completions.create(
            model=model,
            messages=messages,
            temperature=0.7,
            max_tokens=1500,  # 减少token数量以加快响应
            stream=True
        )
        
        # 累积完整响应并同时打印每个部分
        full_response = ""
        print("\n--- 开始接收响应 ---")
        
        response_start_time = time.time()
        last_update_time = response_start_time
        last_progress_time = response_start_time
        chunk_count = 0
        
        for chunk in stream:
            # 获取当前时间
            current_time = time.time()
            
            # 提取内容
            if chunk.choices and chunk.choices[0].delta and chunk.choices[0].delta.content:
                content = chunk.choices[0].delta.content
                full_response += content
                
                # 打印内容
                print(content, end='', flush=True)
                
                # 更新计数器和时间
                chunk_count += 1
                last_update_time = current_time
            
            # 每5秒显示一次进度信息
            elapsed = current_time - last_progress_time
            if elapsed > 5.0:
                total_elapsed = current_time - response_start_time
                print(f"\n[进度更新: {total_elapsed:.1f}秒已过, 已接收{chunk_count}个数据块]", end='', flush=True)
                last_progress_time = current_time
            
            # 如果超过30秒没有新内容，尝试提前结束
            if current_time - last_update_time > 30.0 and chunk_count > 0:
                print("\n\n[警告: 响应停滞超过30秒，提前结束]")
                break
        
        # 计算总响应时间
        total_time = time.time() - response_start_time
        print(f"\n\n--- 响应完成 ---")
        print(f"总用时: {total_time:.2f}秒, 收到{chunk_count}个数据块")
        print(f"响应长度: {len(full_response)} 字符")
        
        return full_response
        
    except Exception as e:
        print(f"API调用出错: {e}")
        
        # 出错时返回默认内容 - 便于调试
        print("使用默认内容替代")
        default_content = {
            "core_insight": "明星崇拜现象本质上是现代社会中的一种替代性宗教体验。在上帝'死亡'的时代，人们将对超验存在的崇拜转向了可见的偶像。",
            "philosopher_perspective": "尼采会将明星崇拜视为'上帝已死'后的价值重估过程，而鲍德里亚则会指出，明星作为符号的空洞性，其影响力不来自真实，而来自其作为'拟像'的完美性。",
            "practical_reflection": "意识到明星崇拜的本质，可以帮助我们反思自己生活中的价值来源。真正的价值应来自自我创造和真实关系，而非对虚构完美形象的追逐。",
            "recommended_reading": ["尼采《欢愉的智慧》", "鲍德里亚《拟像与仿真》", "德波《景观社会》"],
            "reflection_questions": ["我们为何需要偶像？这反映了什么心理需求？", "如果将追星时间用于自我发展，会带来怎样的改变？"]
        }
        
        return json.dumps(default_content, ensure_ascii=False)

def parse_llm_response(response_text, content_type):
    """
    解析LLM返回的内容
    
    参数:
        response_text: LLM返回的文本
        content_type: 内容类型
        
    返回:
        解析后的内容字典
    """
    try:
        # 从响应中提取JSON内容
        json_str = extract_json_from_text(response_text)
        if json_str:
            try:
                content = json.loads(json_str)
                print(f"成功解析JSON响应")
                return content
            except json.JSONDecodeError as e:
                print(f"提取的JSON解析错误: {e}, 尝试进一步处理...")
        
        # 如果不是有效的JSON，进行文本处理
        print("使用文本处理提取内容...")
        # 构建一个基本结构
        if content_type == "philosophy":
            # 尝试从文本中提取关键信息
            content = {
                "core_insight": extract_section(response_text, "核心洞察", "核心观点", "core_insight"),
                "philosopher_perspective": extract_section(response_text, "哲学家视角", "哲学观点", "philosopher_perspective"),
                "practical_reflection": extract_section(response_text, "实践反思", "生活启发", "practical_reflection"),
                "recommended_reading": extract_list(response_text, "推荐阅读", "参考文献", "recommended_reading"),
                "reflection_questions": extract_list(response_text, "思考问题", "反思问题", "reflection_questions")
            }
        elif content_type == "economics":
            content = {
                "value_chain": extract_section(response_text, "价值链", "经济价值", "value_chain"),
                "attention_economics": extract_section(response_text, "注意力经济", "注意力价值", "attention_economics"),
                "market_dynamics": extract_section(response_text, "市场机制", "市场动态", "market_dynamics"),
                "power_structure": extract_section(response_text, "权力结构", "权力关系", "power_structure"),
                "social_cost": extract_section(response_text, "社会成本", "社会代价", "social_cost"),
                "data_evidence": extract_list(response_text, "数据证据", "支撑数据", "data_evidence")
            }
        elif content_type == "creative":
            content = {
                "poetry_version": extract_section(response_text, "诗歌版本", "诗歌改写", "poetry_version"),
                "visual_concept": extract_section(response_text, "视觉概念", "艺术概念", "visual_concept"),
                "narrative_rewrite": extract_section(response_text, "叙事重构", "故事重写", "narrative_rewrite"),
                "artistic_statement": extract_section(response_text, "艺术声明", "艺术宣言", "artistic_statement"),
                "creation_method": extract_section(response_text, "创作方法", "创作技巧", "creation_method"),
                "cultural_elevation": extract_section(response_text, "文化提升", "文化升华", "cultural_elevation")
            }
        elif content_type == "historical":
            content = {
                "historical_parallels": extract_list(response_text, "历史类似现象", "历史对比", "historical_parallels"),
                "evolution_timeline": extract_section(response_text, "演变时间线", "历史演变", "evolution_timeline"),
                "cyclical_patterns": extract_section(response_text, "周期性规律", "历史周期", "cyclical_patterns"),
                "technological_impact": extract_section(response_text, "技术影响", "技术作用", "technological_impact"),
                "future_projection": extract_section(response_text, "未来预测", "未来发展", "future_projection"),
                "lessons_learned": extract_section(response_text, "历史教训", "历史启示", "lessons_learned")
            }
        else:
            # 默认处理
            content = {"main_content": response_text}
        
        # 过滤掉空值
        content = {k: v for k, v in content.items() if v}
        
        # 如果结果为空，返回原始响应
        if not content:
            return {"main_content": response_text}
            
        return content
    except Exception as e:
        print(f"解析响应时出错: {e}")
        return {"main_content": response_text}

def extract_json_from_text(text):
    """从文本中提取JSON内容"""
    # 首先删除无关的行前缀，如'```json'或类似内容
    clean_text = re.sub(r'^```\s*(?:json)?\s*\n?', '', text, flags=re.MULTILINE)
    clean_text = re.sub(r'\n?```\s*$', '', clean_text, flags=re.MULTILINE)
    
    # 预处理：修复常见格式问题，如将换行格式化掉或修复省略号
    # 尝试移除行尾的换行符和续行符
    clean_text = re.sub(r'([^,{}\[\]:])\s*\n\s*', r'\1 ', clean_text)
    # 修复省略号
    clean_text = re.sub(r'\.\.\.\s*"', ' ..."', clean_text)
    
    # 尝试方法1：检查是否有完整的JSON
    if clean_text.strip().startswith('{') and clean_text.strip().endswith('}'):
        try:
            # 尝试直接解析，可能的话返回有效的JSON字符串
            json.loads(clean_text.strip())
            return clean_text.strip()
        except json.JSONDecodeError:
            pass  # 如果无法解析，继续尝试其他方法
    
    # 尝试方法2：在文本中查找JSON块
    try:
        start_pos = clean_text.find('{')
        if start_pos != -1:
            # 尝试找到匹配的右花括号（考虑嵌套）
            brace_count = 1
            for i in range(start_pos + 1, len(clean_text)):
                if clean_text[i] == '{':
                    brace_count += 1
                elif clean_text[i] == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        # 找到了完整的JSON，提取并尝试修复
                        json_str = clean_text[start_pos:i + 1]
                        # 尝试修复不完整的JSON
                        try:
                            json.loads(json_str)
                            return json_str  # 如果成功解析，返回
                        except json.JSONDecodeError:
                            # 尝试修复常见问题
                            fixed_json = fix_json_string(json_str)
                            return fixed_json
    except Exception as e:
        print(f"提取JSON块时出错: {e}")
    
    # 尝试方法3：检查是否有JSON代码块
    json_pattern = r'```(?:json)?\s*([\s\S]*?)```'
    json_match = re.search(json_pattern, text)
    if json_match:
        json_str = json_match.group(1).strip()
        if json_str.startswith('{') and json_str.endswith('}'):
            # 尝试修复不完整的JSON
            return fix_json_string(json_str)
    
    # 如果找不到完整的JSON结构，返回空字符串
    return ""

def fix_json_string(json_str):
    """尝试修复不完整或格式错误的JSON字符串"""
    try:
        # 首先尝试原样解析
        json.loads(json_str)
        return json_str
    except json.JSONDecodeError:
        # 修复常见问题
        original_json = json_str
        
        # 1. 移除行尾注释和多余的符号
        json_str = re.sub(r'//.*?$', '', json_str, flags=re.MULTILINE)
        json_str = re.sub(r'/\*.*?\*/', '', json_str, flags=re.DOTALL)
        
        # 2. 处理省略号和截断的内容
        json_str = re.sub(r'\.{3,}', '...', json_str)
        json_str = re.sub(r'([^"])\.{3}([^"])', r'\1"\2', json_str)
        json_str = re.sub(r'\.{3}"', '..."', json_str)
        
        # 3. 修复缺失的逗号
        # 处理行尾缺少逗号的情况
        json_str = re.sub(r'("[^"]*")\s*\n\s*("[^"]*")', r'\1,\n\2', json_str)
        json_str = re.sub(r'(true|false|null|\d+)\s*\n\s*(")', r'\1,\n\2', json_str)
        json_str = re.sub(r'(}|])\s*\n\s*(")', r'\1,\n\2', json_str)
        
        # 4. 修复键值对之间缺少逗号的情况
        pattern = r'"([^"]*)"\s*:\s*("[^"]*"|[\[\{][\s\S]*?[\]\}]|[^,\{\}\[\]\s]+)\s*("([^"]*)"\s*:)'
        while re.search(pattern, json_str):
            json_str = re.sub(pattern, r'"\1": \2, \3', json_str)
        
        # 5. 处理未闭合的字符串（可能由于省略号截断）
        lines = json_str.split('\n')
        fixed_lines = []
        for line in lines:
            # 计算引号数量，如果是奇数，添加缺失的引号
            quote_count = line.count('"')
            if quote_count % 2 == 1:
                # 如果行尾有省略号，添加引号
                if line.strip().endswith('...'):
                    line = line.rstrip('...') + '..."'
                # 查找最后一个引号位置
                last_quote_pos = line.rfind('"')
                # 检查是否是值的开始但没有结束
                if ':' in line and line.rfind(':') < last_quote_pos:
                    line = line + '"'
                # 检查是否是键的开始但没有结束
                elif ':' in line and line.rfind(':') > last_quote_pos:
                    colon_pos = line.rfind(':')
                    line = line[:colon_pos] + '"' + line[colon_pos:]
            fixed_lines.append(line)
        json_str = '\n'.join(fixed_lines)
        
        # 6. 修复多余的逗号
        json_str = re.sub(r',\s*}', '}', json_str)
        json_str = re.sub(r',\s*]', ']', json_str)
        
        # 7. 修复嵌套对象中的格式问题
        # 处理对象内部缺少引号的键
        unquoted_key_pattern = r'{\s*([a-zA-Z0-9_]+)\s*:'
        while re.search(unquoted_key_pattern, json_str):
            json_str = re.sub(unquoted_key_pattern, r'{"\1":', json_str)
        
        # 8. 尝试修复截断的JSON
        # 如果JSON看起来被截断，尝试补全结构
        if not json_str.strip().endswith('}'):
            # 计算左右大括号的数量
            left_braces = json_str.count('{')
            right_braces = json_str.count('}')
            # 补全缺少的右大括号
            if left_braces > right_braces:
                json_str = json_str.rstrip() + '}' * (left_braces - right_braces)
        
        # 检查方括号是否配对
        left_brackets = json_str.count('[')
        right_brackets = json_str.count(']')
        if left_brackets > right_brackets:
            # 找到最后一个未配对的左括号
            last_open = -1
            brace_count = 0
            for i, char in enumerate(json_str):
                if char == '[':
                    brace_count += 1
                elif char == ']':
                    brace_count -= 1
                if brace_count > 0:
                    last_open = i
            
            if last_open != -1:
                # 在合适的位置添加右括号
                json_str = json_str[:last_open+1] + ']' + json_str[last_open+1:]
        
        try:
            # 尝试解析修复后的JSON
            json.loads(json_str)
            print("JSON修复成功")
            return json_str
        except json.JSONDecodeError as e:
            # 如果第一轮修复失败，尝试更激进的修复
            try:
                print(f"初步修复失败: {e}，尝试更激进的修复...")
                
                # 如果错误是关于特定行和列，尝试定位并修复
                error_msg = str(e)
                # 提取行和列信息
                pos_match = re.search(r'char (\d+)', error_msg)
                if pos_match:
                    pos = int(pos_match.group(1))
                    # 如果错误在JSON中间，尝试修复周围的上下文
                    if 0 < pos < len(json_str):
                        # 查看错误位置前后的字符
                        context = json_str[max(0, pos-20):min(len(json_str), pos+20)]
                        print(f"问题上下文: {context}")
                        
                        # 尝试识别常见错误模式并修复
                        if json_str[pos:pos+1] == ':' and not re.search(r'"[^"]*"$', json_str[:pos]):
                            # 键没有引号
                            key_start = max(0, json_str.rfind(',', 0, pos))
                            if key_start == 0:
                                key_start = json_str.rfind('{', 0, pos)
                            key = json_str[key_start+1:pos].strip()
                            json_str = json_str[:key_start+1] + f' "{key}"' + json_str[pos:]
                
                # 尝试解析修复后的JSON
                json.loads(json_str)
                print("激进修复成功")
                return json_str
            except:
                # 如果仍然无法修复，返回原始字符串
                print("无法修复JSON格式，返回原始文本")
                return original_json

def extract_section(text, *section_names):
    """从文本中提取指定章节的内容"""
    for section_name in section_names:
        # 处理中文段落标题，通常使用冒号
        if any('\u4e00' <= char <= '\u9fff' for char in section_name):
            pattern = rf"{section_name}[：:]\s*(.*?)(?:\n\n|\n[A-Z一-龥]|$)"
            match = re.search(pattern, text, re.DOTALL)
            if match:
                return match.group(1).strip()
        # 处理英文字段名，可能在JSON中
        else:
            # 尝试匹配JSON中的字段，如 "field_name": "value"
            pattern = rf'"{section_name}"\s*:\s*"([^"]*)"'
            match = re.search(pattern, text)
            if match:
                return match.group(1).strip()
            
            # 尝试匹配带引号的数组或对象值
            pattern = rf'"{section_name}"\s*:\s*(\[.*?\]|\{{.*?\}})'
            match = re.search(pattern, text, re.DOTALL)
            if match:
                return match.group(1).strip()
            
            # 尝试匹配英文标题格式，如 "Field Name: content"
            pattern = rf"{section_name}:\s*(.*?)(?:\n\n|\n[A-Z]|$)"
            match = re.search(pattern, text, re.DOTALL | re.IGNORECASE)
            if match:
                return match.group(1).strip()
    return ""

def extract_list(text, *section_names):
    """从文本中提取列表项"""
    # 先尝试直接提取部分
    section_text = ""
    for section_name in section_names:
        # 处理中文段落标题
        if any('\u4e00' <= char <= '\u9fff' for char in section_name):
            pattern = rf"{section_name}[：:](.*?)(?:\n\n|\n[A-Z一-龥]|$)"
            match = re.search(pattern, text, re.DOTALL)
            if match:
                section_text = match.group(1).strip()
                break
        # 处理英文字段名，可能在JSON中
        else:
            # 尝试匹配JSON数组，如 "field_name": ["item1", "item2"]
            pattern = rf'"{section_name}"\s*:\s*(\[.*?\])'
            match = re.search(pattern, text, re.DOTALL)
            if match:
                try:
                    json_array = match.group(1).strip()
                    array_items = json.loads(json_array)
                    if isinstance(array_items, list):
                        return array_items
                except:
                    pass
            
            # 尝试匹配英文标题格式，如 "Field Name: item1, item2"
            pattern = rf"{section_name}:\s*(.*?)(?:\n\n|\n[A-Z]|$)"
            match = re.search(pattern, text, re.DOTALL | re.IGNORECASE)
            if match:
                section_text = match.group(1).strip()
                break
    
    if not section_text:
        return []
    
    # 尝试提取列表项
    items = []
    # 匹配带编号或符号的列表项
    list_items = re.findall(r'(?:^|\n)(?:\d+\.|\*|\-)\s*(.*?)(?:\n|$)', section_text)
    if list_items:
        items.extend([item.strip() for item in list_items if item.strip()])
    else:
        # 尝试按逗号分割（适用于简单的逗号分隔列表）
        if ',' in section_text:
            items.extend([item.strip() for item in section_text.split(',') if item.strip()])
        else:
            # 尝试按行分割
            lines = [line.strip() for line in section_text.split('\n') if line.strip()]
            items.extend(lines)
    
    return items

def create_default_content(content_type, pollution_data):
    """
    创建默认内容，当API调用失败时使用
    
    参数:
        content_type: 内容类型
        pollution_data: 污染信息数据
        
    返回:
        默认内容字典
    """
    keywords = pollution_data.get("keywords", [])
    keyword_text = "、".join(keywords) if keywords else "未知内容"
    
    if content_type == "philosophy":
        return {
            "core_insight": f"从哲学视角分析{keyword_text}",
            "philosopher_perspective": "尼采会将此现象视为人类对超验意义的持续追求",
            "practical_reflection": "反思我们自身的价值来源和注意力分配",
            "recommended_reading": ["尼采《查拉图斯特拉如是说》", "鲍德里亚《消费社会》"],
            "reflection_questions": ["这些内容如何影响我的世界观？", "我可以如何更有意识地分配注意力？"]
        }
    elif content_type == "economics":
        return {
            "value_chain": f"{keyword_text}的经济价值链分析",
            "attention_economics": "此类内容将注意力转化为商业价值的机制",
            "market_dynamics": "供需分析及市场失效",
            "power_structure": "利益相关方权力分析",
            "social_cost": "社会注意力资源的错配成本",
            "data_evidence": ["注意力经济研究", "平台资本主义理论"]
        }
    else:
        return {
            "title": f"{content_type.title()}分析",
            "content": f"对{keyword_text}的深度分析",
            "resources": ["推荐阅读1", "推荐阅读2"]
        }

def format_content_for_display(content, content_type):
    """
    将内容格式化为显示格式
    
    参数:
        content: 内容字典
        content_type: 内容类型
        
    返回:
        格式化后的内容字典，包含title、content和resources
    """
    if content_type == "philosophy":
        title = "哲学反思：现象本质"
        formatted_text = ""
        
        # 收集所有内容片段，以避免重复
        sections = []
        if "core_insight" in content:
            sections.append(f"核心洞察：\n{content['core_insight']}")
        if "philosopher_perspective" in content:
            sections.append(f"哲学家视角：\n{content['philosopher_perspective']}")
        if "practical_reflection" in content:
            sections.append(f"实践反思：\n{content['practical_reflection']}")
        
        # 添加思考问题
        questions_text = ""
        if "reflection_questions" in content:
            questions_text = "思考问题：\n"
            for i, question in enumerate(content['reflection_questions'], 1):
                questions_text += f"{i}. {question}\n"
            sections.append(questions_text)
        
        # 合并所有部分，确保每部分之间有适当的空行
        formatted_text = "\n\n".join(sections)
        
        resources = content.get("recommended_reading", [])
        
        return {
            "title": title,
            "content": formatted_text,
            "resources": resources
        }
    
    elif content_type == "economics":
        title = "经济分析：价值与权力"
        formatted_text = ""
        
        # 收集所有内容片段，以避免重复
        sections = []
        if "value_chain" in content:
            sections.append(f"价值链分析：\n{content['value_chain']}")
        if "attention_economics" in content:
            sections.append(f"注意力经济：\n{content['attention_economics']}")
        if "power_structure" in content:
            sections.append(f"权力结构：\n{content['power_structure']}")
        if "social_cost" in content:
            sections.append(f"社会成本：\n{content['social_cost']}")
        
        # 合并所有部分，确保每部分之间有适当的空行
        formatted_text = "\n\n".join(sections)
        
        resources = content.get("data_evidence", [])
        
        return {
            "title": title,
            "content": formatted_text,
            "resources": resources
        }
    
    elif content_type == "creative":
        title = "创意转换：艺术重构"
        formatted_text = ""
        
        # 收集所有内容片段，以避免重复
        sections = []
        if "poetry_version" in content:
            sections.append(f"诗歌改写：\n{content['poetry_version']}")
        if "visual_concept" in content:
            sections.append(f"视觉概念：\n{content['visual_concept']}")
        if "narrative_rewrite" in content:
            sections.append(f"叙事重构：\n{content['narrative_rewrite']}")
        if "artistic_statement" in content:
            sections.append(f"艺术声明：\n{content['artistic_statement']}")
        
        # 合并所有部分，确保每部分之间有适当的空行
        formatted_text = "\n\n".join(sections)
        
        resources = []
        if "creation_method" in content:
            resources.append(content["creation_method"])
        if "cultural_elevation" in content:
            resources.append(content["cultural_elevation"])
        
        return {
            "title": title,
            "content": formatted_text,
            "resources": resources
        }
    
    elif content_type == "historical":
        title = "历史脉络：现象演变"
        formatted_text = ""
        
        # 收集所有内容片段，以避免重复
        sections = []
        
        # 处理历史类似现象列表
        if "historical_parallels" in content:
            parallels_text = "历史类似现象：\n"
            for i, parallel in enumerate(content['historical_parallels'], 1):
                parallels_text += f"{i}. {parallel}\n"
            sections.append(parallels_text)
            
        if "evolution_timeline" in content:
            sections.append(f"演变时间线：\n{content['evolution_timeline']}")
        if "technological_impact" in content:
            sections.append(f"技术影响：\n{content['technological_impact']}")
        if "future_projection" in content:
            sections.append(f"未来预测：\n{content['future_projection']}")
        
        # 合并所有部分，确保每部分之间有适当的空行
        formatted_text = "\n\n".join(sections)
        
        resources = []
        if "lessons_learned" in content:
            resources.append(content["lessons_learned"])
        if "cyclical_patterns" in content:
            resources.append(content["cyclical_patterns"])
        
        return {
            "title": title,
            "content": formatted_text,
            "resources": resources
        }
    
    else:
        # 通用格式
        return {
            "title": f"{content_type.title()}分析",
            "content": str(content),
            "resources": []
        }

# 内容缓存字典
_content_cache = {}
_cache_size_limit = 50  # 最多缓存50个内容

def cache_content(key, content):
    """缓存内容"""
    global _content_cache
    
    # 如果缓存过大，移除最早的内容
    if len(_content_cache) >= _cache_size_limit:
        # 删除第一个添加的内容
        oldest_key = next(iter(_content_cache))
        del _content_cache[oldest_key]
    
    # 添加到缓存
    _content_cache[key] = content

def get_cached_content(key):
    """获取缓存的内容"""
    # 暂时禁用缓存功能，始终返回None以强制重新生成内容
    # 这将解决连续生成相同类型内容时的简化问题
    return None
    
    # 原有缓存逻辑（已禁用）
    # return _content_cache.get(key)