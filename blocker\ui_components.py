import os
from PyQt5.QtWidgets import QLabel
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QPixmap, QCursor
from PyQt5.QtWidgets import QApplication

class MouseTipWidget(QLabel):
    """
    鼠标提示小图标
    """
    clicked = pyqtSignal()
    interrupt_clicked = pyqtSignal()  # 新增中断信号
    
    def __init__(self):
        super().__init__()
        self.is_processing = False
        self.initUI()
        
    def initUI(self):
        # 设置窗口标志
        self.setWindowFlags(
            Qt.FramelessWindowHint |
            Qt.WindowStaysOnTopHint |
            Qt.Tool
        )
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # 加载图片路径
        script_dir = os.path.dirname(os.path.abspath(__file__))
        self.mouse_path = os.path.join(script_dir, "resources", "mouse.png")
        self.eagle_path = os.path.join(script_dir, "resources", "神鹰.png")  # 确保这是静态图片
        
        self.size = 96  # 图标大小
        
        # 初始显示鼠标图标
        self.show_mouse_icon()
        
        # 移动到屏幕右下角
        screen = QApplication.primaryScreen().geometry()
        self.move(screen.width() - self.size - 20, screen.height() - self.size - 20)
        
        # 设置鼠标样式
        self.setCursor(QCursor(Qt.PointingHandCursor))
    
    def show_mouse_icon(self):
        mouse_pixmap = QPixmap(self.mouse_path)
        if not mouse_pixmap.isNull():
            scaled_pixmap = mouse_pixmap.scaled(self.size, self.size, 
                                              Qt.KeepAspectRatio, 
                                              Qt.SmoothTransformation)
            self.setPixmap(scaled_pixmap)
            self.setFixedSize(self.size, self.size)
    
    def show_eagle_icon(self):
        eagle_pixmap = QPixmap(self.eagle_path)
        if not eagle_pixmap.isNull():
            scaled_pixmap = eagle_pixmap.scaled(self.size, self.size, 
                                              Qt.KeepAspectRatio, 
                                              Qt.SmoothTransformation)
            self.setPixmap(scaled_pixmap)
            self.setFixedSize(self.size, self.size)
        
    def start_processing(self):
        self.is_processing = True
        self.show_eagle_icon()
        self.setCursor(QCursor(Qt.PointingHandCursor))  # 保持鼠标手型
        
    def stop_processing(self):
        self.is_processing = False
        self.show_mouse_icon()
        self.setCursor(QCursor(Qt.PointingHandCursor))
        
    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            if self.is_processing:
                self.interrupt_clicked.emit()  # 处理状态下点击发出中断信号
            else:
                self.clicked.emit()  # 非处理状态下发出普通点击信号
            
    def enterEvent(self, event):
        if not self.is_processing:
            self.setStyleSheet("background-color: rgba(200, 200, 200, 50); border-radius: 24px;")
        
    def leaveEvent(self, event):
        if not self.is_processing:
            self.setStyleSheet("")