# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Jupyter Notebook
.ipynb_checkpoints

# PyCharm
.idea/
*.iml
*.iws
*.ipr
.idea_modules/
out/

# VS Code
.vscode/
*.code-workspace
.history/

# Cursor
# .cursor/

# Windows specific
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Mac OS specific
.DS_Store
.AppleDouble
.LSOverride
._*

# Project specific
*.log
logs/
temp/
tmp/

# Virtual Environment
venv/
env/
ENV/

# Coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover 
client/__pycache__/advanced_overlay.cpython-312.pyc
