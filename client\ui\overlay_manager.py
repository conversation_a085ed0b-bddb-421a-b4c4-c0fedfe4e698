import json
import cv2
import numpy as np
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt, pyqtSignal, QObject
from ui.advanced_overlay import AdvancedOverlay
from core.resistance_manager import ResistanceManager
from core.constant import OverlayType
from core.config import OverlayConfig, ImageConfig

class OverlayManager(QObject):
    """
    遮罩管理器
    """
    update_signal = pyqtSignal(dict)
    
    def __init__(self, downsample_rate=None, parent=None):
        super().__init__(parent)
        self.overlays = []
        self.update_signal.connect(self.update_overlays, Qt.QueuedConnection)
        self.last_image = None
        
        # 使用配置文件中的值
        self.similarity_threshold = ImageConfig.SIMILARITY_THRESHOLD
        self.downsample_rate = downsample_rate or ImageConfig.get_downsample_rate_value()
        
        # 创建抵抗方案管理器
        self.resistance_manager = ResistanceManager()
        
        # 遮罩显示设置
        self.overlay_settings = OverlayConfig.DEFAULT_OVERLAY_SETTINGS.copy()
    
    def get_overlay_settings(self):
        """获取当前遮罩显示设置"""
        return self.overlay_settings
    
    def toggle_overlay_type(self, flag_type):
        """切换遮罩类型的显示状态"""
        if flag_type in self.overlay_settings:
            self.overlay_settings[flag_type] = not self.overlay_settings[flag_type]
            print(f"遮罩类型 {flag_type} 已{'启用' if self.overlay_settings[flag_type] else '禁用'}")
            return True
        return False
    
    def process_response(self, response_json):
        """处理LLM返回的JSON响应，创建或更新遮罩"""
        try:
            data = json.loads(response_json)
            print("解析成功:", data)
            
            # 发送更新信号，保持与原始代码兼容
            self.update_signal.emit(data)
            
            # 以下是直接处理逻辑，后续可以考虑替换update_overlays方法
            if not isinstance(data, dict):
                print("警告: 返回的数据不是字典格式")
                return
                
            # 提取位置信息
            position = data.get("position", [0, 0, 100, 100])
            
            # 验证位置数据
            if not isinstance(position, list) or len(position) != 4:
                print(f"警告: 无效的位置数据 {position}")
                return
                
            # 确保所有位置值都是数值类型
            try:
                x1, y1, x2, y2 = map(int, position)
            except (ValueError, TypeError):
                print(f"警告: 位置数据类型错误 {position}")
                return
                
            # 确保坐标有效（x2 > x1 且 y2 > y1）
            if x2 <= x1 or y2 <= y1:
                print(f"警告: 无效的坐标范围 {position}")
                # 尝试修复坐标
                if x2 <= x1:
                    x2 = x1 + 100
                if y2 <= y1:
                    y2 = y1 + 100
            
            # 应用下采样率计算实际坐标
            x1 = int(x1 / self.downsample_rate)
            y1 = int(y1 / self.downsample_rate)
            x2 = int(x2 / self.downsample_rate)
            y2 = int(y2 / self.downsample_rate)
            
            # 确保坐标在合理范围内
            screen = QApplication.primaryScreen()
            screen_geometry = screen.availableGeometry()
            max_width = screen_geometry.width()
            max_height = screen_geometry.height()
            
            # 防止坐标超出屏幕范围
            x1 = max(0, min(x1, max_width - 10))
            y1 = max(0, min(y1, max_height - 10))
            x2 = max(x1 + 50, min(x2, max_width))
            y2 = max(y1 + 50, min(y2, max_height))
            
            # 计算宽度和高度
            width = x2 - x1
            height = y2 - y1
            
            # 限制尺寸在合理范围内
            width = min(width, 800)  # 最大宽度
            height = min(height, 600)  # 最大高度
            width = max(width, 100)  # 最小宽度
            height = max(height, 100)  # 最小高度
            
            # 提取其他信息
            keywords = data.get("keywords", [])
            if isinstance(keywords, str):
                keywords = [keywords]  # 确保keywords是列表
                
            comment = data.get("comment", "")
            flag = data.get("flag", "Normal")
            
            # 根据不同的遮罩类型处理
            if flag in self.overlay_settings and self.overlay_settings[flag]:
                # 检查是否已经存在相同位置的遮罩
                for overlay in self.overlays:
                    if self.is_same_position(overlay, x1, y1, width, height):
                        print(f"已存在相同位置的遮罩，跳过创建")
                        return
                
                # 创建抵抗方案包
                resistance_package = None
                
                # 如果是污染信息，尝试生成抵抗方案
                if flag == "Evil":
                    resistance_package = self.resistance_manager.generate_resistance_package(data)
                
                # 创建新的高级遮罩 - 使用精确坐标
                print(f"创建遮罩，精确位置: x={x1}, y={y1}, w={width}, h={height}")
                
                # 应用位置偏移量
                x1 += OverlayConfig.POSITION_OFFSET_X
                y1 += OverlayConfig.POSITION_OFFSET_Y
                
                overlay = AdvancedOverlay(
                    x1, y1, width, height,
                    keywords=keywords,
                    context=comment,
                    resistance_package=resistance_package
                )
                
                # 添加到遮罩列表
                self.overlays.append(overlay)
                
                # 显示遮罩
                overlay.show()
                print(f"已创建遮罩: 位置=({x1}, {y1}, {width}, {height}), 类型={flag}")
            
        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {e}")
        except Exception as e:
            print(f"处理响应时出错: {e}")
            
    def is_same_position(self, overlay, x, y, width, height):
        """检查是否已经存在相同位置的遮罩"""
        # 获取遮罩的几何信息
        overlay_geometry = overlay.geometry()
        overlay_x = overlay_geometry.x()
        overlay_y = overlay_geometry.y()
        overlay_width = overlay_geometry.width()
        overlay_height = overlay_geometry.height()
        
        # 计算重叠程度
        overlap_x = max(0, min(overlay_x + overlay_width, x + width) - max(overlay_x, x))
        overlap_y = max(0, min(overlay_y + overlay_height, y + height) - max(overlay_y, y))
        overlap_area = overlap_x * overlap_y
        
        # 如果重叠面积超过任一遮罩面积的70%，认为是相同位置
        overlay_area = overlay_width * overlay_height
        new_area = width * height
        min_area = min(overlay_area, new_area)
        
        return (overlap_area / min_area) > 0.7 if min_area > 0 else False
    
    def update_overlays(self, response_data):
        try:
            print("开始更新遮罩")
            # 将单个对象转换为列表
            items = [response_data] if isinstance(response_data, dict) else response_data
            
            # 根据解析的内容创建新遮罩
            for item in items:
                if 'position' in item:
                    overlay = self.create_overlay(item)
                    if overlay:
                        self.overlays.append(overlay)
                        print(f"显示遮罩: {item['position']}")
                        overlay.show()
                        QApplication.processEvents()
        except Exception as e:
            print(f"更新遮罩时出错: {e}")
    
    def clear_all_overlays(self):
        print("清除所有遮罩")
        for overlay in self.overlays:
            overlay.hide()
            overlay.deleteLater()
        self.overlays.clear()
        QApplication.processEvents()
    
    def check_image_similarity(self, current_image):
        """检查当前图像与上一帧的相似度"""
        if self.last_image is None:
            self.last_image = current_image
            return False
        
        try:
            # 将图像调整为相同大小
            current_small = cv2.resize(current_image, (320, 180))  # 缩小以加快计算
            last_small = cv2.resize(self.last_image, (320, 180))
            
            # 计算图像差异
            diff = cv2.absdiff(current_small, last_small)
            diff_sum = np.sum(diff)
            max_diff = current_small.size * 255
            similarity = 1 - (diff_sum / max_diff)
            
            print(f"图像相似度: {similarity:.4f}")
            
            # 更新last_image
            self.last_image = current_image.copy()
            
            # 如果相似度低于阈值，说明画面发生了剧烈变化
            return similarity < self.similarity_threshold
            
        except Exception as e:
            print(f"计算图像相似度时出错: {e}")
            return False
    
    def create_overlay(self, item):
        try:
            position = item['position']
            # 将缩小的坐标还原到原始尺寸
            scale_factor = 1 / self.downsample_rate
            x1, y1, x2, y2 = [int(coord * scale_factor) for coord in position]
            
            # 确保坐标有效
            if x2 <= x1:
                x2 = x1 + 100
            if y2 <= y1:
                y2 = y1 + 100
            
            # 精确计算尺寸
            x = x1
            y = y1
            w = x2 - x1
            h = y2 - y1
            
            # 应用位置偏移量
            x += OverlayConfig.POSITION_OFFSET_X
            y += OverlayConfig.POSITION_OFFSET_Y
            
            # 限制尺寸在合理范围内
            screen = QApplication.primaryScreen()
            screen_geometry = screen.availableGeometry()
            w = min(w, 800)  # 最大宽度
            h = min(h, 600)  # 最大高度
            w = max(w, 100)  # 最小宽度
            h = max(h, 100)  # 最小高度
            
            # 确保在屏幕范围内
            if x + w > screen_geometry.width():
                x = max(0, screen_geometry.width() - w - 10)
            if y + h > screen_geometry.height():
                y = max(0, screen_geometry.height() - h - 10)
            
            print(f"创建遮罩，位置: x={x}, y={y}, w={w}, h={h}")
            
            # 为该内容生成抵抗方案包
            resistance_package = self.resistance_manager.generate_resistance_package(item)
            
            overlay = AdvancedOverlay(
                x, y, w, h, 
                keywords=item.get('keywords', []),
                context=item.get('comment', ""),
                resistance_package=resistance_package
            )
            return overlay
        except Exception as e:
            print(f"创建遮罩时出错: {e}")
            return None
    
    def set_downsample_rate(self, rate):
        """设置下采样率"""
        if rate > 0:
            self.downsample_rate = rate
            print(f"遮罩管理器下采样率已设置为: {rate}")
            return True
        return False