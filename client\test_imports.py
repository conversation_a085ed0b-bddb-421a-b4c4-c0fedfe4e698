"""
测试导入模块是否正常工作
"""
import sys
import os

# 添加项目根目录到系统路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 添加所有子目录到系统路径
for subdir in ['core', 'ui', 'llm', 'utils', 'resources']:
    path = os.path.join(current_dir, subdir)
    if path not in sys.path and os.path.isdir(path):
        sys.path.append(path)

print("测试导入核心模块...")
try:
    from core.main import MainWindow
    from core.resistance_manager import ResistanceManager
    from core.config import SystemConfig
    from core.constant import ResistanceMode
    print("✓ 核心模块导入成功")
except Exception as e:
    print(f"✗ 核心模块导入失败: {e}")

print("\n测试导入UI模块...")
try:
    from ui.advanced_overlay import AdvancedOverlay
    from ui.overlay_manager import OverlayManager
    from ui.ui_components import MouseTipWidget
    print("✓ UI模块导入成功")
except Exception as e:
    print(f"✗ UI模块导入失败: {e}")

print("\n测试导入LLM模块...")
# 跳过完整模块导入，只测试函数导入
try:
    from llm.demo_llm import get_upload_policy, upload_file_to_oss, image_array_to_base64
    from llm.cal_token import calculate_token_cost
    print("✓ LLM模块关键函数导入成功")
except Exception as e:
    print(f"✗ LLM模块导入失败: {e}")

print("\n测试导入资源模块...")
try:
    from resources.llm.prompt import dirty_prompt
    from resources.templates.resistance_methods import RESISTANCE_METHODS
    print("✓ 资源模块导入成功")
except Exception as e:
    print(f"✗ 资源模块导入失败: {e}")

print("\n测试导入提示词生成器...")
try:
    from resources.llm.prompt_based_generator import generate_prompt_based_content
    print("✓ 提示词生成器导入成功")
except Exception as e:
    print(f"✗ 提示词生成器导入失败: {e}")

print("\n测试导入增强抵抗加载器...")
try:
    from resources.templates.enhanced_resistance_loader import get_advanced_resistance_content
    print("✓ 增强抵抗加载器导入成功")
except Exception as e:
    print(f"✗ 增强抵抗加载器导入失败: {e}")

print("\n所有测试完成。") 