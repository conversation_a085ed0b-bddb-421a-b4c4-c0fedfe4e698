"""
污染信息抵抗系统 - 启动入口
"""
import sys
import os

# 将当前目录添加到系统路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 添加所有子目录到系统路径
for subdir in ['core', 'ui', 'llm', 'utils', 'resources']:
    path = os.path.join(current_dir, subdir)
    if path not in sys.path and os.path.isdir(path):
        sys.path.append(path)

# 导入主应用程序
from core.main import main

if __name__ == "__main__":
    # 启动主应用程序
    main() 