import win32gui
import win32process
import psutil
import time

class ApplicationMonitor:
    def __init__(self):
        self.current_app = None
        self.app_templates = {
            "微博(浏览器)": {
                "content_areas": [(0.1, 0.2, 0.9, 0.8)],
                "ignore_areas": [(0, 0, 1.0, 0.15)]
            },
            "微信": {
                "content_areas": [(0.2, 0.1, 0.8, 0.9)],
                "ignore_areas": [(0, 0.9, 1.0, 1.0)]
            }
        }
    
    def check_app_switch(self):
        """检查应用是否切换，并返回当前应用信息"""
        window_info = get_foreground_window_info()
        app_type = identify_application(window_info)
        
        if app_type != self.current_app:
            print(f"应用切换: {self.current_app} -> {app_type}")
            self.current_app = app_type
            return True, app_type, self.get_app_template(app_type)
        
        return False, app_type, None
    
    def get_app_template(self, app_type):
        """获取应用的模板配置"""
        return self.app_templates.get(app_type, {
            "content_areas": [(0, 0, 1.0, 1.0)],
            "ignore_areas": []
        })

def get_foreground_window_info():
    """获取当前前台窗口的详细信息"""
    # 获取前台窗口句柄
    hwnd = win32gui.GetForegroundWindow()
    
    # 获取窗口标题
    window_title = win32gui.GetWindowText(hwnd)
    
    # 获取窗口类名
    window_class = win32gui.GetClassName(hwnd)
    
    # 获取窗口位置和大小
    rect = win32gui.GetWindowRect(hwnd)
    x, y, width, height = rect[0], rect[1], rect[2] - rect[0], rect[3] - rect[1]
    
    # 获取进程ID
    _, process_id = win32process.GetWindowThreadProcessId(hwnd)
    
    # 获取进程名称
    try:
        process = psutil.Process(process_id)
        process_name = process.name()
        process_path = process.exe()
    except (psutil.NoSuchProcess, psutil.AccessDenied):
        process_name = "Unknown"
        process_path = "Unknown"
    
    return {
        "hwnd": hwnd,
        "title": window_title,
        "class": window_class,
        "position": (x, y, width, height),
        "process_id": process_id,
        "process_name": process_name,
        "process_path": process_path
    }

def monitor_foreground_window():
    """监控前台窗口变化"""
    last_window = None
    
    while True:
        current_window = get_foreground_window_info()
        
        # 检测窗口是否变化
        if last_window is None or current_window["hwnd"] != last_window["hwnd"]:
            print("前台窗口已切换:")
            print(f"标题: {current_window['title']}")
            print(f"进程: {current_window['process_name']}")
            print(f"位置: {current_window['position']}")
            print("-" * 50)
            
            # 在这里添加您的应用识别逻辑
            identify_application(current_window)
            
            last_window = current_window
        
        time.sleep(0.5)  # 每0.5秒检查一次

def identify_application(window_info):
    """根据窗口信息识别应用类型"""
    title = window_info["title"].lower()
    process = window_info["process_name"].lower()
    
    # 识别常见应用
    if "chrome" in process or "msedge" in process or "firefox" in process:
        app_type = "浏览器"
        # 进一步分析标题可以识别具体网站
        if "微博" in title:
            app_type = "微博(浏览器)"
        elif "twitter" in title or "推特" in title:
            app_type = "Twitter(浏览器)"
    elif "wechat" in process or "微信" in process:
        app_type = "微信"
    elif "dingtalk" in process or "钉钉" in process:
        app_type = "钉钉"
    elif "code" in process and "visual studio code" in title.lower():
        app_type = "VS Code"
    elif "pycharm" in process:
        app_type = "PyCharm"
    else:
        app_type = f"其他应用({process})"
    
    print(f"识别应用类型: {app_type}")
    return app_type