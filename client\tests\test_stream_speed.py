"""
测试LLM和VLM流式输出的速度和响应情况
"""

from resources.prompt_based_generator import call_llm_api
from demo_llm import analyze_image_stream
from PIL import Image
import numpy as np
from config import SystemConfig
import json
import time

def test_llm_stream_speed():
    """测试LLM流式输出的速度"""
    print("=== 测试LLM流式输出速度 ===")
    
    # 创建多个不同长度的提示词进行测试
    test_prompts = [
        # 短提示词
        {
            "name": "短提示词 (简单分析)",
            "prompt": """
            你是一位哲学学者，请简要分析明星崇拜现象的哲学意义，并以JSON格式输出结果。
            """
        },
        # 中等长度提示词
        {
            "name": "中等提示词 (标准分析)",
            "prompt": """
            你是一位哲学学者，请分析明星崇拜现象的哲学意义，并以JSON格式输出结果，包含以下字段:
            - core_insight: 核心哲学洞察
            - philosopher_perspective: 相关哲学家观点
            - practical_reflection: 对个人生活的启发
            - recommended_reading: 推荐阅读列表
            - reflection_questions: 思考问题列表
            """
        },
        # 长提示词
        {
            "name": "长提示词 (详细分析)",
            "prompt": """
            你是一位哲学学者，专精于将日常现象与哲学理论联系。
            
            任务：请分析明星崇拜现象的哲学意义
            
            请从以下哲学角度分析：
            
            1. **存在论维度**：这种现象反映了怎样的存在状态？
            2. **认识论维度**：它如何影响人们的认知方式？
            3. **价值论维度**：体现了什么样的价值观冲突？
            4. **美学维度**：从审美角度如何理解这种现象？
            
            请选择最相关的哲学家观点进行分析：
            - 从尼采、叔本华、萨特的视角
            - 从阿多诺、本雅明、鲍德里亚的媒体批判视角
            - 从海德格尔、德勒兹、阿甘本的复杂理论视角
            
            请以JSON格式输出，包含以下字段：
            - core_insight: 核心哲学洞察
            - philosopher_perspective: 相关哲学家观点（对象数组）
            - practical_reflection: 对个人生活的启发
            - recommended_reading: 推荐阅读列表
            - reflection_questions: 思考问题列表
            """
        }
    ]
    
    # 依次测试不同长度的提示词
    for test_case in test_prompts:
        print(f"\n\n测试用例: {test_case['name']}")
        print("-" * 50)
        
        # 记录开始时间
        start_time = time.time()
        
        # 调用API
        response = call_llm_api(test_case["prompt"])
        
        # 计算总耗时
        total_time = time.time() - start_time
        
        # 输出结果摘要
        print(f"\n测试结果:")
        print(f"总耗时: {total_time:.2f}秒")
        if response:
            print(f"响应长度: {len(response)} 字符")
            
            # 尝试解析JSON
            try:
                json_data = json.loads(response)
                print(f"成功解析为JSON对象，包含 {len(json_data)} 个字段")
            except json.JSONDecodeError:
                print("响应不是有效的JSON格式")
        else:
            print("未收到有效响应")
    
    print("\n=== LLM测试完成 ===")

def test_vlm_stream_speed():
    """测试VLM流式输出的速度"""
    print("\n=== 测试VLM流式输出速度 ===")
    
    # 获取API密钥
    api_key = SystemConfig.DEFAULT_API_KEY
    if not api_key:
        print("错误：未配置API密钥")
        return
    
    # 加载测试图片
    try:
        test_image_path = r"..\blocker\resources\news_testpic11.jpg"
        image = Image.open(test_image_path)
        image_array = np.array(image)
        print(f"成功加载测试图片：{test_image_path}")
    except Exception as e:
        print(f"加载图片出错: {e}")
        return
    
    # 创建多个不同长度的提示词进行测试
    test_prompts = [
        # 短提示词
        {
            "name": "短提示词 (简单描述)",
            "prompt": "请简要描述这张图片的内容。"
        },
        # 中等长度提示词
        {
            "name": "中等提示词 (标准分析)",
            "prompt": """
            请分析这张图片的内容，并回答以下问题：
            1. 图片中有哪些主要元素？
            2. 这些内容属于什么类型？
            3. 图片传达了什么信息？
            """
        },
        # 长提示词
        {
            "name": "长提示词 (详细分析)",
            "prompt": """
            你是一个专业的污染信息分析师。请分析这张图片中的内容，并输出详细分析结果。
            
            请包含以下信息：
            1. 污染类型：这是什么类型的信息？（例如：明星、娱乐、广告等）
            2. 关键元素：图片中的关键对象、人物、文字等
            3. 潜在影响：这种内容可能对用户产生的影响
            4. 详细描述：对图片内容的全面、客观描述
            
            请以JSON格式输出，包含以下字段：
            {
              "content_type": "内容类型",
              "keywords": ["关键词1", "关键词2", ...],
              "description": "详细客观描述",
              "potential_impact": "潜在影响分析"
            }
            """
        }
    ]
    
    # 依次测试不同长度的提示词
    for test_case in test_prompts:
        print(f"\n\n测试用例: {test_case['name']}")
        print("-" * 50)
        
        # 记录开始时间
        start_time = time.time()
        
        # 获取VLM分析结果
        print(f"\n调用VLM API... 使用模型: {SystemConfig.DEFAULT_VLM_MODEL}")
        
        try:
            # 流式调用VLM
            stream = analyze_image_stream(
                image_array, 
                api_key, 
                model_name=SystemConfig.DEFAULT_VLM_MODEL,
                prompt=test_case["prompt"]
            )
            
            # 收集完整响应
            full_response = ""
            chunk_count = 0
            
            print("\n--- 开始接收VLM响应 ---")
            for chunk in stream:
                if chunk.choices[0].delta.content is not None:
                    content = chunk.choices[0].delta.content
                    full_response += content
                    print(content, end='', flush=True)
                    chunk_count += 1
            
            # 计算总耗时
            total_time = time.time() - start_time
            
            print(f"\n\n--- 响应完成 ---")
            print(f"总用时: {total_time:.2f}秒, 收到{chunk_count}个数据块")
            print(f"响应长度: {len(full_response)} 字符")
            
        except Exception as e:
            print(f"VLM调用出错: {e}")
            total_time = time.time() - start_time
            print(f"错误发生时总耗时: {total_time:.2f}秒")
    
    print("\n=== VLM测试完成 ===")

if __name__ == "__main__":
    test_llm_stream_speed()
    test_vlm_stream_speed() 