"""
测试抵抗内容的显示格式，验证修复的效果
"""

from resources.prompt_based_generator import generate_prompt_based_content
from resources.enhanced_resistance_loader import format_enhanced_resistance_for_display_from_content

def test_display_format():
    """测试内容显示格式"""
    print("=== 测试抵抗内容显示格式 ===")
    
    # 创建模拟的污染数据
    test_pollution_data = {
        "type": "CELEBRITY",
        "keywords": ["明星", "娱乐", "热搜"],
        "comment": "某明星在社交媒体上发布了新动态，引发大量讨论",
        "raw_description": "这是一条关于明星的热搜信息"
    }
    
    # 生成不同类型的抵抗内容
    content_types = ["philosophy", "economics", "creative", "historical"]
    
    for content_type in content_types:
        print(f"\n测试 {content_type} 类型内容显示:")
        print("-" * 50)
        
        # 生成内容
        content = generate_prompt_based_content(content_type, test_pollution_data)
        
        # 打印内容的原始结构
        print(f"标题: {content.get('title', '无标题')}")
        print(f"内容长度: {len(content.get('content', ''))}")
        print(f"资源数量: {len(content.get('resources', []))}")
        
        # 使用格式化函数处理内容
        formatted_content = format_enhanced_resistance_for_display_from_content(content)
        
        # 检查是否有标题重复
        title = content.get('title', '')
        if title in formatted_content[:len(title) + 10]:  # 检查开头是否包含标题
            print("警告: 格式化文本中可能存在标题重复!")
        
        # 打印格式化后的内容（仅显示前200个字符）
        print("\n格式化后内容预览:")
        preview = formatted_content[:200] + "..." if len(formatted_content) > 200 else formatted_content
        print(preview)
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_display_format() 