"""
污染信息抵抗系统 - 抵抗方案管理器
负责生成和管理不同类型的抵抗方案
"""

import os
import random
from resources.templates.resistance_methods import (
    RESISTANCE_METHODS, 
    POLLUTION_TYPES,
    POLLUTION_TO_RESISTANCE_MAP,
    CRIME_TEMPLATES,
    CULTURE_VALUE_TEMPLATES,
    RESISTANCE_TYPES
)
from resources.templates.enhanced_resistance_loader import get_advanced_resistance_content
from core.config import ResourceConfig, RESOURCES_DIR, ResistanceConfig
from core.constant import ResistanceMethods, ResistanceMode, ResistanceType, ResistanceSelectionMode

# 内容生成模式
GENERATION_MODE_EXAMPLE = "example"  # 使用预定义例子
GENERATION_MODE_PROMPT = "prompt"    # 使用提示词生成

# 方法ID到抵抗类型的映射
method_to_type_map = {
    "CELEBRITY_SYMBOL_ANALYSIS": "symbol_analysis",
    "CELEBRITY_ECONOMIC_ANALYSIS": "economic_analysis",
    "CELEBRITY_PHILOSOPHICAL_REFLECTION": "philosophical_card",
    "ENTERTAINMENT_NEWS_DECODING": "symbol_analysis",
    "AD_DESIRE_ENGINEERING": "symbol_analysis",
    "CONSUMPTION_PHILOSOPHY": "philosophical_card"
}

class ResistanceManager:
    """抵抗方案管理器，负责生成和管理不同类型的抵抗方案"""
    
    def __init__(self):
        """初始化抵抗方案管理器"""
        # 资源路径
        self.resource_path = RESOURCES_DIR
        # 缓存已加载的抵抗方案资源
        self.resource_cache = {}
        # 当前抵抗模式
        self.resistance_mode = ResistanceConfig.DEFAULT_MODE
        # 当前抵抗方案选择模式
        self.selection_mode = ResistanceConfig.DEFAULT_SELECTION_MODE
        # 内容生成模式: "example"(预定义例子) 或 "prompt"(提示词生成)
        self.generation_mode = ResistanceConfig.DEFAULT_GENERATION_MODE  # 使用配置中的默认值
        
    def get_resource_path(self, resource_name):
        """获取资源的完整路径"""
        return ResourceConfig.get_resource_path(resource_name)
    
    def has_resource(self, resource_name):
        """检查资源是否存在"""
        resource_path = self.get_resource_path(resource_name)
        return os.path.exists(resource_path)
    
    def set_resistance_mode(self, mode):
        """设置抵抗模式"""
        if isinstance(mode, ResistanceMode):
            self.resistance_mode = mode
            return True
        return False
    
    def get_resistance_mode(self):
        """获取当前抵抗模式"""
        return self.resistance_mode
    
    def set_selection_mode(self, mode):
        """设置抵抗方案选择模式"""
        if isinstance(mode, ResistanceSelectionMode):
            self.selection_mode = mode
            return True
        return False
    
    def get_selection_mode(self):
        """获取当前抵抗方案选择模式"""
        return self.selection_mode
    
    def set_generation_mode(self, mode):
        """设置内容生成模式"""
        if mode in [GENERATION_MODE_EXAMPLE, GENERATION_MODE_PROMPT]:
            self.generation_mode = mode
            print(f"内容生成模式已设置为: {mode}")
            return True
        return False
    
    def get_generation_mode(self):
        """获取当前内容生成模式"""
        return self.generation_mode
    
    def determine_pollution_type(self, keywords, content_type):
        """根据关键词和内容类型确定污染类型"""
        # 简单映射规则
        if any(kw in ["明星", "艺人", "歌手", "演员"] for kw in keywords) or "明星" in content_type:
            return "CELEBRITY"
        elif any(kw in ["广告", "促销", "购物"] for kw in keywords) or "广告" in content_type:
            return "ADVERTISEMENT"
        elif any(kw in ["娱乐", "综艺", "八卦"] for kw in keywords) or "娱乐" in content_type:
            return "ENTERTAINMENT"
        elif "热搜" in content_type or any(kw in ["热搜", "话题"] for kw in keywords):
            return "HOT_TOPIC"
        else:
            return "LOW_QUALITY"  # 默认为低质量内容
    
    def generate_crime(self, pollution_type, keywords):
        """生成罪名"""
        template = CRIME_TEMPLATES.get(pollution_type, "罪名：污染视觉空间罪")
        
        # 提取关键信息进行替换
        if pollution_type == "CELEBRITY" and keywords:
            return template.format(name=keywords[0] if keywords else "某明星")
        elif pollution_type == "ENTERTAINMENT" and keywords:
            return template.format(content=keywords[0] if keywords else "某娱乐内容")
        elif pollution_type == "ADVERTISEMENT" and keywords:
            return template.format(brand=keywords[0] if keywords else "某品牌")
        elif pollution_type == "HOT_TOPIC" and keywords:
            return template.format(topic=keywords[0] if keywords else "某热搜话题")
        else:
            return template
    
    def generate_culture_value(self, pollution_type):
        """生成文化价值解读"""
        return CULTURE_VALUE_TEMPLATES.get(pollution_type, "捍卫信息获取权，抵制污染信息")
    
    def get_resistance_methods(self, pollution_type):
        """获取适合该污染类型的抵抗方案列表"""
        method_keys = POLLUTION_TO_RESISTANCE_MAP.get(pollution_type, ["MASK", "SKULL"])
        return [RESISTANCE_METHODS[key] for key in method_keys if key in RESISTANCE_METHODS]
    
    def recommend_resistance_method(self, pollution_type):
        """推荐一个抵抗方案"""
        # 获取适合该污染类型的所有抵抗方案
        method_keys = POLLUTION_TO_RESISTANCE_MAP.get(pollution_type, ["MASK", "SKULL"])
        methods = [key for key in method_keys if key in RESISTANCE_METHODS]
        
        print(f"推荐抵抗方案 - 污染类型: {pollution_type}")
        print(f"可用方法: {methods}")
        print(f"选择模式: {self.selection_mode}")
        print(f"抵抗模式: {self.resistance_mode}")
        
        if not methods:
            print("未找到可用方法，使用默认遮罩")
            return RESISTANCE_METHODS["MASK"]  # 默认使用遮罩
        
        # 根据选择模式推荐抵抗方案 - 选择模式独立于抵抗模式
        if self.selection_mode == ResistanceSelectionMode.ADVANCED_ONLY:
            # 优先选择高级抵抗方案
            advanced_methods = [m for m in methods if RESISTANCE_METHODS[m]["type"] == "ADVANCED"]
            print(f"高级方案模式 - 可用高级方法: {advanced_methods}")
            if advanced_methods:
                selected = random.choice(advanced_methods)
                print(f"选择高级方法: {selected}")
                return RESISTANCE_METHODS[selected]
            print("未找到高级方法，继续其他选择")
        
        elif self.selection_mode == ResistanceSelectionMode.BASIC_ONLY:
            # 只选择基础抵抗方案
            basic_methods = [m for m in methods if RESISTANCE_METHODS[m]["type"] == "BASIC"]
            print(f"基础方案模式 - 可用基础方法: {basic_methods}")
            if basic_methods:
                selected = random.choice(basic_methods)
                print(f"选择基础方法: {selected}")
                return RESISTANCE_METHODS[selected]
                
        elif self.selection_mode == ResistanceSelectionMode.CREATIVE_ONLY:
            # 优先选择创意抵抗方案
            creative_methods = [m for m in methods if RESISTANCE_METHODS[m]["type"] == "CREATIVE"]
            print(f"创意方案模式 - 可用创意方法: {creative_methods}")
            if creative_methods:
                selected = random.choice(creative_methods)
                print(f"选择创意方法: {selected}")
                return RESISTANCE_METHODS[selected]
                
        elif self.selection_mode == ResistanceSelectionMode.ACADEMIC_ONLY:
            # 优先选择学术解构方案
            academic_methods = [m for m in methods if RESISTANCE_METHODS[m]["type"] == "ACADEMIC"]
            print(f"学术方案模式 - 可用学术方法: {academic_methods}")
            if academic_methods:
                selected = random.choice(academic_methods)
                print(f"选择学术方法: {selected}")
                return RESISTANCE_METHODS[selected]
                
        elif self.selection_mode == ResistanceSelectionMode.ART_ONLY:
            # 优先选择终极艺术武器方案
            art_methods = [m for m in methods if RESISTANCE_METHODS[m]["type"] == "ART"]
            print(f"艺术方案模式 - 可用艺术方法: {art_methods}")
            if art_methods:
                selected = random.choice(art_methods)
                print(f"选择艺术方法: {selected}")
                return RESISTANCE_METHODS[selected]
                
        elif self.selection_mode == ResistanceSelectionMode.RANDOM:
            # 随机逻辑：25%概率选择高级方案
            if random.random() < 0.25:
                advanced_methods = [m for m in methods if RESISTANCE_METHODS[m]["type"] == "ADVANCED"]
                print(f"随机模式 - 可用高级方法: {advanced_methods}")
                if advanced_methods:
                    selected = random.choice(advanced_methods)
                    print(f"随机选择了高级方法: {selected}")
                    return RESISTANCE_METHODS[selected]
            selected = random.choice(methods)
            print(f"随机选择了方法: {selected}")
            return RESISTANCE_METHODS[selected]
        
        # 如果根据选择模式没有找到合适的方案，从所有方案中随机选择一个
        selected = random.choice(methods)
        print(f"未匹配选择模式，随机选择: {selected}")
        return RESISTANCE_METHODS[selected]
    
    def generate_resistance_package(self, content_data):
        """生成完整的抵抗方案包"""
        keywords = content_data.get("keywords", [])
        content_type = content_data.get("type", "")
        
        # 检查是否有详细分析信息
        detailed_analysis_str = content_data.get("detailed_analysis_str", "")
        
        # 调试输出 - 当前配置信息
        print(f"\n=== 抵抗方案生成 ===")
        print(f"关键词: {keywords}")
        print(f"内容类型: {content_type}")
        print(f"抵抗模式: {self.resistance_mode}")
        print(f"选择模式: {self.selection_mode}")
        print(f"生成模式: {self.generation_mode}")
        print(f"详细分析: {True if detailed_analysis_str else False}")
        
        # 确定污染类型
        pollution_type = self.determine_pollution_type(keywords, content_type)
        print(f"识别的污染类型: {pollution_type}")
        
        # 获取可用的抵抗方案
        method_keys = POLLUTION_TO_RESISTANCE_MAP.get(pollution_type, ["MASK", "SKULL"])
        methods = [key for key in method_keys if key in RESISTANCE_METHODS]
        print(f"可用的抵抗方案: {methods}")
        
        # 获取推荐的抵抗方案
        resistance_method = self.recommend_resistance_method(pollution_type)
        print(f"推荐的抵抗方案: {resistance_method['name']} (类型: {resistance_method['type']})")
        
        # 生成罪名和文化价值
        crime = self.generate_crime(pollution_type, keywords)
        culture_value = self.generate_culture_value(pollution_type)
        
        # 创建抵抗方案包
        resistance_package = {
            "pollution_type": pollution_type,
            "pollution_name": POLLUTION_TYPES.get(pollution_type, "未知污染"),
            "method": resistance_method,
            "crime": crime,
            "culture_value": culture_value,
            "keywords": keywords,
            "original_comment": content_data.get("comment", ""),
            "resistance_mode": self.resistance_mode.value
        }
        
        # 处理不同类型的抵抗方案
        method_type = resistance_method.get("type", "")
        
        # 处理高级抵抗方案
        if method_type == "ADVANCED":
            print(f"生成高级抵抗内容...")
            method_name = resistance_method.get("name", "")
            method_id = next((key for key, method in RESISTANCE_METHODS.items() 
                             if method == resistance_method), "")
            print(f"方法ID: {method_id}")
            
            # 如果有详细分析，添加到数据中
            if detailed_analysis_str:
                # 将详细分析添加到content_data
                enhanced_data = content_data.copy()
                enhanced_data["detailed_analysis"] = detailed_analysis_str
                enhanced_data["raw_description"] = content_data.get("comment", "")
                print(f"使用详细分析数据生成抵抗内容")
            else:
                enhanced_data = content_data
            
            # 使用enhanced_resistance_loader获取高级抵抗内容
            advanced_result = get_advanced_resistance_content(
                method_id=method_id,
                pollution_type=pollution_type,
                keywords=keywords,
                generation_mode=self.generation_mode,  # 传递生成模式
                pollution_data=enhanced_data  # 传递增强数据
            )
            
            # 获取主要内容
            main_content = advanced_result.get("main_content", "")
            
            # 更新抵抗方案包
            resistance_package["content"] = main_content
            resistance_package["all_contents"] = advanced_result.get("all_contents", {})
            resistance_package["primary_type"] = method_to_type_map.get(method_id, "")
            print(f"获取到高级抵抗内容 - 类别数量: {len(advanced_result.get('all_contents', {}))}")
            
            # 添加学习资源
            if "resources" in resistance_method:
                resistance_package["learning_resources"] = resistance_method["resources"]
            
        # 处理模板类型的抵抗方案
        elif "template" in resistance_method:
            print(f"生成模板抵抗内容...")
            template = resistance_method["template"]
            # 简单替换，实际应用中可以更复杂
            if pollution_type == "CELEBRITY" and keywords:
                resistance_package["content"] = template.format(name=keywords[0] if keywords else "某明星")
            elif pollution_type == "ADVERTISEMENT" and keywords:
                resistance_package["content"] = template.format(brand=keywords[0] if keywords else "某品牌")
            elif pollution_type == "HOT_TOPIC" and keywords:
                resistance_package["content"] = template.format(topic=keywords[0] if keywords else "某热搜话题")
            elif pollution_type == "ENTERTAINMENT" and "narrative_type" in template:
                narrative_types = ["明星崛起", "情感纠葛", "争议风波"]
                resistance_package["content"] = template.format(narrative_type=random.choice(narrative_types))
            else:
                resistance_package["content"] = template
        
        return resistance_package 