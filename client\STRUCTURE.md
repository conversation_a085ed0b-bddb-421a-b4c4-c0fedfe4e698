# 污染信息抵抗系统 - 项目结构

## 目录结构

```
client/
├── core/                   # 核心功能和业务逻辑
│   ├── main.py             # 主程序入口
│   ├── resistance_manager.py # 抵抗方案管理器
│   ├── window_monitor.py   # 窗口监控
│   ├── analyzer.py         # 内容分析
│   ├── config.py           # 系统配置
│   └── constant.py         # 常量定义
│
├── ui/                     # 用户界面相关
│   ├── advanced_overlay.py # 高级遮罩
│   ├── overlay_manager.py  # 遮罩管理器
│   └── ui_components.py    # UI组件
│
├── llm/                    # 大语言模型集成
│   ├── demo_llm.py         # LLM调用演示
│   └── cal_token.py        # Token计算
│
├── resources/              # 资源文件
│   ├── assets/             # 图片和多媒体资源
│   │   ├── mouse.png
│   │   ├── skull.png
│   │   └── ...
│   ├── llm/                # LLM提示词和模板
│   │   ├── prompt.py       # 基础提示词
│   │   ├── enhanced_prompt.py # 增强提示词
│   │   └── prompt_based_generator.py # 基于提示词的生成器
│   └── templates/          # 抵抗方案模板
│       ├── resistance_methods.py # 抵抗方法定义
│       ├── enhanced_resistance_loader.py # 增强抵抗内容加载器
│       └── enhanced_resistance_examples.py # 增强抵抗示例
│
├── tests/                  # 测试文件
│   ├── test_api_call.py    # API调用测试
│   ├── test_ui_display.py  # UI显示测试
│   └── ...
│
├── utils/                  # 工具类
│   └── poop.py             # 特效工具
│
├── run.py                  # 启动入口
└── README.md               # 项目说明
```

## 模块说明

### core - 核心模块

此模块包含应用程序的主要业务逻辑和功能：

- `main.py`: 应用程序的主入口，管理主窗口和UI元素
- `resistance_manager.py`: 管理和生成各类抵抗方案
- `window_monitor.py`: 监控系统窗口，用于捕获屏幕内容
- `analyzer.py`: 分析内容和提取信息
- `config.py`: 系统配置参数
- `constant.py`: 系统常量定义

### ui - 用户界面模块

此模块包含所有与用户界面相关的组件：

- `advanced_overlay.py`: 高级遮罩实现，用于显示抵抗内容
- `overlay_manager.py`: 管理多个遮罩的创建和显示
- `ui_components.py`: 通用UI组件如鼠标提示等

### llm - 大语言模型集成

此模块处理与大语言模型的交互：

- `demo_llm.py`: 演示如何调用LLM分析图像内容
- `cal_token.py`: 计算Token消耗

### resources - 资源文件

包含系统所需的各类资源：

- `assets/`: 图片和多媒体资源
- `llm/`: LLM相关的提示词和模板
- `templates/`: 抵抗方案的各种模板

### tests - 测试模块

包含所有测试文件，用于验证系统功能：

- 各种单元测试和集成测试

### utils - 工具类

包含辅助功能和工具：

- `poop.py`: 特效相关工具

## 启动方式

项目可以通过运行根目录下的`run.py`文件启动：

```bash
python run.py
```

## 开发指南

新增功能时，请遵循以下原则：

1. 核心业务逻辑放在`core`目录
2. UI相关代码放在`ui`目录
3. LLM调用相关代码放在`llm`目录
4. 测试代码放在`tests`目录
5. 资源文件根据类型放在`resources`相应子目录 