"""
测试完整的内容生成流程：从VLM分析到基于提示词生成抵抗内容
"""

import sys
import numpy as np
from PIL import Image
import json
from config import SystemConfig
from demo_llm import analyze_image_stream
from resources.prompt_based_generator import generate_prompt_based_content

def test_full_content_generation():
    """测试完整的内容生成流程"""
    print("=== 测试完整的内容生成流程 ===")
    
    # 1. 配置
    api_key = SystemConfig.DEFAULT_API_KEY
    if not api_key:
        print("错误：未配置API密钥")
        return
    
    # 2. 加载测试图片
    try:
        test_image_path = r"..\blocker\resources\news_testpic11.jpg"
        image = Image.open(test_image_path)
        image_array = np.array(image)
        print(f"成功加载测试图片：{test_image_path}")
    except Exception as e:
        print(f"加载图片出错: {e}")
        return
    
    # 3. 使用VLM进行图片分析
    print("\n第一步：使用VLM进行图片分析...")
    vlm_prompt = """
    你是一个专业的污染信息分析师。请分析这张图片中的污染内容，并输出详细分析结果。
    
    请包含以下信息：
    1. 污染类型：这是什么类型的污染信息？（例如：色情、暴力、恐怖、消极情绪等）
    2. 关键元素：图片中的关键对象、人物、文字等
    3. 潜在影响：这种内容可能对用户产生的负面影响
    4. 详细描述：对图片内容的全面、客观描述
    
    请以JSON格式输出，包含以下字段：
    {
      "pollution_type": "污染类型",
      "keywords": ["关键词1", "关键词2", ...],
      "description": "详细客观描述",
      "potential_impact": "潜在影响分析"
    }
    """
    
    try:
        # 获取VLM分析结果
        stream = analyze_image_stream(
            image_array, 
            api_key, 
            model_name=SystemConfig.DEFAULT_VLM_MODEL,
            prompt=vlm_prompt
        )
        
        # 收集完整响应
        full_response = ""
        print("\nVLM分析结果:")
        for chunk in stream:
            if chunk.choices[0].delta.content is not None:
                content = chunk.choices[0].delta.content
                full_response += content
                print(content, end='', flush=True)
        print("\n")
        
        # 尝试解析JSON响应
        try:
            # 提取JSON部分
            json_start = full_response.find('{')
            json_end = full_response.rfind('}') + 1
            if json_start != -1 and json_end != -1:
                json_str = full_response[json_start:json_end]
                vlm_result = json.loads(json_str)
                print("成功解析VLM分析结果")
            else:
                # 如果没有找到JSON，使用整个响应
                print("警告：未找到JSON格式，使用原始响应")
                vlm_result = {
                    "pollution_type": "未知",
                    "keywords": ["测试"],
                    "description": full_response,
                    "potential_impact": "未知"
                }
        except json.JSONDecodeError:
            print("警告：VLM响应不是有效的JSON格式，使用原始响应")
            vlm_result = {
                "pollution_type": "未知",
                "keywords": ["测试"],
                "description": full_response,
                "potential_impact": "未知"
            }
    except Exception as e:
        print(f"VLM分析出错: {e}")
        return
    
    # 4. 准备污染数据，包含VLM分析结果
    pollution_data = {
        "type": vlm_result.get("pollution_type", "未知类型"),
        "keywords": vlm_result.get("keywords", []),
        "comment": "测试图片",
        "raw_description": vlm_result.get("description", ""),
        "detailed_analysis": f"类型：{vlm_result.get('pollution_type', '未知')}，描述：{vlm_result.get('description', '')}，潜在影响：{vlm_result.get('potential_impact', '')}"
    }
    
    # 5. 使用提示词生成抵抗内容
    print("\n第二步：生成抵抗内容...")
    content_types = ["philosophy", "economics", "creative", "historical"]
    
    for content_type in content_types:
        print(f"\n生成 {content_type} 类型的抵抗内容:")
        try:
            content = generate_prompt_based_content(content_type, pollution_data)
            print(f"\n标题: {content.get('title', '无标题')}")
            print("-" * 50)
            print(content.get('content', '无内容'))
            print("-" * 50)
            print("资源推荐:")
            for resource in content.get('resources', []):
                print(f"- {resource}")
            print("\n")
        except Exception as e:
            print(f"生成 {content_type} 内容时出错: {e}")
    
    print("=== 测试完成 ===")

if __name__ == "__main__":
    test_full_content_generation() 