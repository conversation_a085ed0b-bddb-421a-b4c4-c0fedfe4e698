import os
from PyQt5.QtWidgets import <PERSON>A<PERSON><PERSON>, QWidget, QVBoxLayout, QLabel, QHBoxLayout
from PyQt5.QtCore import Qt, QPoint
from PyQt5.QtGui import QPainter, QColor, QBrush, QPixmap

class AdvancedOverlay(QWidget):
    def __init__(self, x, y, w, h, keywords=None, context=""):
        super().__init__()
        self.keywords = keywords if keywords else []
        self.context = context
        self.content_visible = False
        self.initUI(x, y, w, h)
        self.dragging = False
        self.offset = QPoint()
        
    def initUI(self, x, y, w, h):
        # 基本窗口设置
        self.setWindowFlags(
            Qt.FramelessWindowHint | 
            Qt.WindowStaysOnTopHint |
            Qt.Tool
        )
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setGeometry(x, y, w, h)
        
        # 创建主布局
        self.main_layout = QHBoxLayout()
        self.setLayout(self.main_layout)
        
        # 左侧骷髅头
        self.skull_label = QLabel(self)
        script_dir = os.path.dirname(os.path.abspath(__file__))
        skull_path = os.path.join(script_dir, "resources", "skull.png")
        
        skull_pixmap = QPixmap(skull_path)
        if not skull_pixmap.isNull():
            skull_size = min(w//3, h)  # 调整骷髅头大小为宽度的1/3
            scaled_pixmap = skull_pixmap.scaled(skull_size, skull_size, 
                                              Qt.KeepAspectRatio, 
                                              Qt.SmoothTransformation)
            self.skull_label.setPixmap(scaled_pixmap)
            self.skull_label.setFixedSize(skull_size, skull_size)
        
        self.main_layout.addWidget(self.skull_label)
        
        # 右侧内容区域
        self.right_widget = QWidget()
        self.right_layout = QVBoxLayout(self.right_widget)
        
        # 关键词标签
        self.keywords_label = QLabel("关键词: " + ", ".join(self.keywords))
        self.keywords_label.setStyleSheet("""
            color: white;
            font-size: 12px;
            background-color: rgba(0, 0, 0, 0.7);
            padding: 5px;
            border-radius: 5px;
        """)
        self.keywords_label.setWordWrap(True)
        
        # 内容标签
        self.context_label = QLabel(self.context)
        self.context_label.setStyleSheet("""
            color: white;
            font-size: 12px;
            background-color: rgba(0, 0, 0, 0.7);
            padding: 5px;
            border-radius: 5px;
        """)
        self.context_label.setWordWrap(True)
        
        self.right_layout.addWidget(self.keywords_label)
        self.right_layout.addWidget(self.context_label)
        
        # 默认隐藏右侧内容
        self.right_widget.hide()
        self.main_layout.addWidget(self.right_widget)
        
        # 设置布局间距
        self.main_layout.setSpacing(5)
        self.main_layout.setContentsMargins(5, 5, 5, 5)

    def enterEvent(self, event):
        if QApplication.keyboardModifiers() == Qt.ControlModifier and self.width() > 150:
            self.right_widget.show()
            
    def leaveEvent(self, event):
        self.right_widget.hide()
        
    def mousePressEvent(self, event):
        if event.modifiers() == Qt.ControlModifier:
            self.dragging = True
            self.offset = event.pos()
            
    def mouseMoveEvent(self, event):
        if self.dragging and event.modifiers() == Qt.ControlModifier:
            self.move(self.mapToParent(event.pos() - self.offset))
            
    def mouseReleaseEvent(self, event):
        self.dragging = False
        
    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        brush = QBrush(QColor(0, 0, 0, 180))
        painter.fillRect(self.rect(), brush)