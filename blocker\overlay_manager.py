import json
import cv2
import numpy as np
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt, pyqtSignal, QObject
from advanced_overlay import AdvancedOverlay

class OverlayManager(QObject):
    """
    遮罩管理器
    """
    update_signal = pyqtSignal(dict)
    
    def __init__(self, downsample_rate=0.5, parent=None):
        super().__init__(parent)
        self.overlays = []
        self.update_signal.connect(self.update_overlays, Qt.QueuedConnection)
        self.last_image = None
        self.similarity_threshold = 0.95  # 相似度阈值，可以根据需要调整
        self.downsample_rate = downsample_rate
    
    def process_response(self, content):
        try:
            response_data = json.loads(content)
            print("解析成功:", response_data)
            self.update_signal.emit(response_data)
        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {e}, content: {content}")
        except Exception as e:
            print(f"处理响应时出错: {e}")
    
    def update_overlays(self, response_data):
        try:
            print("开始更新遮罩")
            # 将单个对象转换为列表
            items = [response_data] if isinstance(response_data, dict) else response_data
            
            # 根据解析的内容创建新遮罩
            for item in items:
                if 'position' in item:
                    overlay = self.create_overlay(item)
                    if overlay:
                        self.overlays.append(overlay)
                        print(f"显示遮罩: {item['position']}")
                        overlay.show()
                        QApplication.processEvents()
        except Exception as e:
            print(f"更新遮罩时出错: {e}")
    
    def clear_all_overlays(self):
        print("清除所有遮罩")
        for overlay in self.overlays:
            overlay.hide()
            overlay.deleteLater()
        self.overlays.clear()
        QApplication.processEvents()
    
    def check_image_similarity(self, current_image):
        """检查当前图像与上一帧的相似度"""
        if self.last_image is None:
            self.last_image = current_image
            return False
        
        try:
            # 将图像调整为相同大小
            current_small = cv2.resize(current_image, (320, 180))  # 缩小以加快计算
            last_small = cv2.resize(self.last_image, (320, 180))
            
            # 计算图像差异
            diff = cv2.absdiff(current_small, last_small)
            diff_sum = np.sum(diff)
            max_diff = current_small.size * 255
            similarity = 1 - (diff_sum / max_diff)
            
            print(f"图像相似度: {similarity:.4f}")
            
            # 更新last_image
            self.last_image = current_image.copy()
            
            # 如果相似度低于阈值，说明画面发生了剧烈变化
            return similarity < self.similarity_threshold
            
        except Exception as e:
            print(f"计算图像相似度时出错: {e}")
            return False
    
    def create_overlay(self, item):
        try:
            position = item['position']
            # 将缩小的坐标还原到原始尺寸
            scale_factor = 1 / self.downsample_rate
            x1, y1, x2, y2 = [int(coord * scale_factor) for coord in position]
            
            x = x1
            y = y1
            w = x2 - x1
            h = y2 - y1
            
            overlay = AdvancedOverlay(x, y, w, h, 
                                  keywords=item.get('keywords', []),
                                  context=item.get('comment', ""))
            return overlay
        except Exception as e:
            print(f"创建遮罩时出错: {e}")
            return None

# 控制选项
OVERLAY_SETTINGS = {
    "Evil": True,   # Evil 内容默认显示
    "Normal": False,  # Normal 内容默认不显示
    "Good": False    # Good 内容默认不显示
}

def toggle_overlay_type(flag_type):
    """切换遮罩类型的显示状态"""
    global OVERLAY_SETTINGS
    OVERLAY_SETTINGS[flag_type] = not OVERLAY_SETTINGS[flag_type]
    print(f"遮罩类型 {flag_type} 已{'启用' if OVERLAY_SETTINGS[flag_type] else '禁用'}")