"""
污染信息抵抗系统 - ASCII 图标资源
由于无法添加真实图像资源，我们使用ASCII艺术作为替代方案
"""

def generate_poop_icon():
    """生成大便图标的ASCII表示"""
    return """
     _____
    /     \\
   /       \\
  |  💩💩   |
  |  💩💩   |
   \\       /
    \\_____/
    """

def generate_mask_icon():
    """生成遮罩图标的ASCII表示"""
    return """
    +-------+
    |///////|
    |///////|
    |///////|
    |///////|
    +-------+
    """

def generate_skull_icon():
    """生成骷髅图标的ASCII表示"""
    return """
     _____
    /     \\
   | ☠️ ☠️  |
   |  ___  |
    \\_____/
    """

# 为了在ASCII字符串中使用表情符号，需要确保Python环境支持UTF-8编码
# 如果在控制台看到乱码，可能需要检查终端编码设置

if __name__ == "__main__":
    print("这是ASCII图标资源模块，不应直接运行。")
    print("以下是图标示例：")
    print(generate_poop_icon())
    print(generate_mask_icon())
    print(generate_skull_icon()) 