"""
污染信息抵抗系统 - 配置管理
集中管理系统中的各种配置项和默认参数
"""

import os
from constant import PromptMode, OverlayType, DownsampleRate, ResourcePaths, ResistanceMode, PollutionType, ResistanceSelectionMode

# 资源目录路径
RESOURCES_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "resources")

# ======== 系统配置 ========
class SystemConfig:
    """系统全局配置"""
    # 应用检查间隔 (毫秒)
    APP_CHECK_INTERVAL = 500
    
    # API 配置
    DEFAULT_API_KEY = "sk-718363330d3242e3a64e5c1aebfb8856"
    DEFAULT_MODEL = "qwen2.5-vl-72b-instruct"  # 视觉分析模型
    DEFAULT_VLM_MODEL = "qwen2.5-vl-72b-instruct"  # 视觉语言模型
    CONTENT_GEN_MODEL = "qwen-max"  # 内容生成专用模型
    
    # 调试模式
    DEBUG_MODE = False
    
    # 图像分析间隔限制 (毫秒) - 防止频繁分析
    MIN_ANALYSIS_INTERVAL = 2000

# ======== 图像处理相关 ========
class ImageConfig:
    """图像处理相关配置"""
    # 默认下采样率
    DEFAULT_DOWNSAMPLE_RATE = DownsampleRate.HIGH_QUALITY # DownsampleRate.BALANCED
    
    # 当前使用的下采样率 - 可在运行时修改
    CURRENT_DOWNSAMPLE_RATE = DEFAULT_DOWNSAMPLE_RATE
    
    # 图像相似度阈值 (0-1)
    SIMILARITY_THRESHOLD = 0.95
    
    # 图标尺寸
    ICON_SIZE = 96
    
    @classmethod
    def get_downsample_rate_value(cls):
        """获取当前下采样率的值"""
        return cls.CURRENT_DOWNSAMPLE_RATE.value
    
    @classmethod
    def set_downsample_rate(cls, rate):
        """设置下采样率
        参数:
            rate: DownsampleRate枚举值
        """
        if isinstance(rate, DownsampleRate):
            cls.CURRENT_DOWNSAMPLE_RATE = rate
            return True
        return False
    
    @classmethod
    def get_downsample_rate_name(cls):
        """获取当前下采样率的名称"""
        names = {
            DownsampleRate.HIGH_QUALITY: "高质量(1.0)",
            DownsampleRate.BALANCED: "平衡模式(0.5)",
            DownsampleRate.PERFORMANCE: "性能模式(0.25)"
        }
        return names.get(cls.CURRENT_DOWNSAMPLE_RATE, "未知")

# ======== 提示词配置 ========
class PromptConfig:
    """提示词配置"""
    # 默认提示词模式
    DEFAULT_MODE = PromptMode.ENHANCED # PromptMode.DIRTY

# ======== 抵抗模式配置 ========
class ResistanceConfig:
    """抵抗模式配置"""
    # 默认抵抗模式
    DEFAULT_MODE = ResistanceMode.MASK
    
    # 默认抵抗方案选择模式
    DEFAULT_SELECTION_MODE = ResistanceSelectionMode.ADVANCED_ONLY # ResistanceSelectionMode.RANDOM
    
    # 默认内容生成模式
    DEFAULT_GENERATION_MODE = "prompt"  # "prompt" 或 "example"
    
    # 污染类型到图标的映射
    POLLUTION_ICON_MAP = {
        PollutionType.CELEBRITY.value: ResourcePaths.POOP_ICON,
        PollutionType.ENTERTAINMENT.value: ResourcePaths.POOP_ICON,
        PollutionType.ADVERTISEMENT.value: ResourcePaths.SKULL_ICON,
        PollutionType.LOW_QUALITY.value: ResourcePaths.SKULL_ICON,
        PollutionType.HOT_TOPIC.value: ResourcePaths.SKULL_ICON,
    }
    
    # 获取污染类型对应的图标
    @classmethod
    def get_icon_for_pollution(cls, pollution_type):
        """根据污染类型获取对应的图标"""
        icon_filename = cls.POLLUTION_ICON_MAP.get(pollution_type, ResourcePaths.SKULL_ICON)
        print(f"为污染类型 {pollution_type} 获取图标: {icon_filename}")
        
        # 检查图标文件是否存在，不存在则使用备用图标
        icon_path = ResourceConfig.get_resource_path(icon_filename)
        if not os.path.exists(icon_path):
            print(f"警告: 图标文件 {icon_filename} 不存在，使用骷髅图标作为备用")
            return ResourcePaths.SKULL_ICON
            
        return icon_filename

# ======== 遮罩设置 ========
class OverlayConfig:
    """遮罩配置"""
    # 默认显示设置
    DEFAULT_OVERLAY_SETTINGS = {
        OverlayType.EVIL.value: True,     # 默认显示污染信息
        OverlayType.NORMAL.value: False,  # 默认不显示普通信息
        OverlayType.GOOD.value: False     # 默认不显示有价值信息
    }
    
    # 遮罩透明度
    OVERLAY_OPACITY = 180  # 0-255
    
    # 遮罩位置偏移量（像素）- 用于微调遮罩位置
    # 正值向右/向下偏移，负值向左/向上偏移
    POSITION_OFFSET_X = 0
    POSITION_OFFSET_Y = 0

# ======== 资源文件路径配置 ========
class ResourceConfig:
    """资源文件路径配置"""
    # 获取完整资源路径
    @staticmethod
    def get_resource_path(filename):
        """获取资源文件的完整路径"""
        # 定义多个可能的资源路径
        base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))  # client目录
        possible_paths = [
            os.path.join(RESOURCES_DIR, "assets", filename),
            os.path.join(RESOURCES_DIR, filename),
            os.path.join(base_dir, "resources", "assets", filename),
            os.path.join(base_dir, "resources", filename),
            os.path.join(base_dir, filename)
        ]
        
        # 遍历可能的路径，返回第一个存在的路径
        for path in possible_paths:
            if os.path.exists(path):
                print(f"找到资源: {path}")
                return path
                
        # 如果找不到，打印警告并返回原始路径
        print(f"警告: 无法找到资源 {filename}，使用默认路径")
        return os.path.join(RESOURCES_DIR, filename)
    
    # 获取图标路径
    @staticmethod
    def get_icon_path(resistance_method=None, pollution_type=None):
        """根据抵抗方法和污染类型获取图标路径"""
        from constant import ResistanceMethods, ResourcePaths
        
        # 如果提供了污染类型，优先使用污染类型对应的图标
        if pollution_type:
            icon_filename = ResistanceConfig.get_icon_for_pollution(pollution_type)
            return ResourceConfig.get_resource_path(icon_filename)
        
        # 否则使用抵抗方法对应的图标
        if resistance_method:
            # 基础抵抗方案的图标映射
            icon_mapping = {
                ResistanceMethods.MASK.value: ResourceConfig.get_resource_path(ResourcePaths.SKULL_ICON),
                ResistanceMethods.POOP.value: ResourceConfig.get_resource_path(ResourcePaths.POOP_ICON),
                ResistanceMethods.SKULL.value: ResourceConfig.get_resource_path(ResourcePaths.SKULL_ICON)
            }
            
            # 高级抵抗方案的图标映射
            # 明星相关高级抵抗方案使用大便图标
            if resistance_method.startswith("CELEBRITY_"):
                return ResourceConfig.get_resource_path(ResourcePaths.POOP_ICON)
            # 娱乐新闻相关高级抵抗方案使用大便图标
            elif resistance_method.startswith("ENTERTAINMENT_"):
                return ResourceConfig.get_resource_path(ResourcePaths.POOP_ICON)
            # 广告相关高级抵抗方案使用骷髅图标
            elif resistance_method.startswith("AD_") or resistance_method == "CONSUMPTION_PHILOSOPHY":
                return ResourceConfig.get_resource_path(ResourcePaths.SKULL_ICON)
            
            return icon_mapping.get(resistance_method, 
                                  ResourceConfig.get_resource_path(ResourcePaths.SKULL_ICON))
        
        # 默认返回骷髅图标
        return ResourceConfig.get_resource_path(ResourcePaths.SKULL_ICON) 