# 污染信息抵抗系统 (Information Pollution Resistance System)

## 项目概述

污染信息抵抗系统是一个创新的信息过滤与文化批评工具，旨在通过智能识别、创意解构和艺术化转换，帮助用户抵抗注意力经济时代的信息污染。本系统不仅仅是一个屏蔽工具，更是一个文化批评和媒体素养教育平台。

### 核心理念

> "将垃圾信息转化为反思的养料，让每一次污染都成为觉醒的契机。"

我们相信，面对信息污染最好的方式不是视而不见，而是通过深度解构、艺术再创造和哲学反思，将其转化为促进批判思维和文化觉醒的资源。

## 核心价值观

- **信息主权**: 每个人都有获取高质量信息的权利
- **文化觉醒**: 通过批判性思维提升集体文化品味
- **创意抵抗**: 用艺术和幽默对抗文化工业的操控
- **知识民主**: 让媒体素养和文化批评工具普及化

## 系统特性

### 1. 智能污染识别
- **多模态分析**: 结合视觉语言模型(VLM)和文本分析
- **实时检测**: 3秒内完成屏幕内容的污染源识别
- **分类精准**: 区分明星八卦、商业广告、低质内容、热搜炒作等

### 2. 多层次抵抗方案

#### 基础抵抗
- 视觉遮罩和警告标识
- 污染源统计和量化分析
- 简单的反向信息提供

#### 高级抵抗
- **符号学解析**: 解构媒体符号的隐含意义
- **经济利益分析**: 揭示内容背后的资本逻辑
- **历史脉络**: 将当前现象置于文化史中考察
- **哲学反思**: 从存在主义角度批判消费文化

#### 创意抵抗
- **艺术再创造**: 将广告转化为概念艺术作品
- **诗歌解构**: 把明星八卦改写为批判诗歌
- **反向拼贴**: 创建讽刺性的视觉作品
- **社会实验**: 设计思维实验揭示荒谬性

#### 学术解构
- **文化研究**: 从文化学角度分析现象
- **媒体理论**: 运用传播学理论解读内容
- **社会学分析**: 考察内容的社会功能和影响
- **心理学透视**: 分析操控心理的技术手段

### 3. 个性化配置
- **抵抗模式**: 遮罩/提示/观察三种模式
- **内容偏好**: 自定义污染类型和抵抗策略
- **学习轨迹**: 根据用户兴趣推荐深度内容

### 4. 社区生态 (规划中)
- **集体智慧**: 众包污染源识别和抵抗方案创作
- **文化沙龙**: 围绕污染现象进行深度讨论
- **创作工坊**: 协作生产批判性艺术作品
- **学习群组**: 媒体素养和文化理论学习

## 技术架构

### 客户端架构
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   屏幕监控模块   │───→│    VLM分析引擎    │───→│   抵抗方案生成   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
          │                       │                       │
          ↓                       ↓                       ↓
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   窗口管理器    │    │   内容识别模块    │    │   遮罩渲染系统   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### 核心组件
- **分析器 (analyzer.py)**: VLM内容分析和污染识别
- **抵抗管理器 (resistance_manager.py)**: 抵抗方案生成和管理
- **遮罩系统 (advanced_overlay.py)**: 高级UI遮罩和内容展示
- **窗口监控 (window_monitor.py)**: 应用窗口状态管理

## 安装和使用

### 环境要求
- Python 3.8+
- PyQt5
- OpenCV
- 其他依赖见 requirements.txt

### 快速开始
```bash
# 克隆项目
git clone [project-url]

# 安装依赖
pip install -r requirements.txt

# 配置API密钥
# 编辑 config.py 设置您的VLM API密钥

# 运行系统
python main.py
```

### 基本操作
1. **启动检测**: 点击鼠标图标开始分析当前屏幕
2. **查看内容**: Ctrl+鼠标悬停查看详细抵抗内容
3. **切换模式**: 右键菜单选择不同的抵抗模式
4. **拖拽遮罩**: Ctrl+拖拽移动遮罩位置

## 开发路线图

### 阶段一: 个人增强器 ✅
- [x] 基础污染识别
- [x] 多种抵抗模式
- [x] 高级遮罩系统
- [x] 用户配置管理

### 阶段二: 内容生成增强 (进行中)
- [ ] 改进LLM内容生成质量
- [ ] 扩展抵抗方案类型
- [ ] 添加学习资源链接
- [ ] 优化实时性能

### 阶段三: 社区平台
- [ ] 污染信息抵抗Hub网站
- [ ] 用户贡献系统
- [ ] 集体创作工具
- [ ] 社区治理机制

### 阶段四: 生态完善
- [ ] 跨平台支持
- [ ] 开放API
- [ ] 第三方插件系统
- [ ] 与艺术机构合作

## 社会意义

### 文化层面
- **提升文化品味**: 通过优质内容替代低俗信息
- **培养批判思维**: 教会用户识别和分析媒体操控
- **保护注意力资源**: 将有限的注意力用于有价值的内容

### 教育层面
- **媒体素养普及**: 让每个人都能理解媒体运作机制
- **文化理论实践**: 将学术理论转化为日常工具
- **创意能力培养**: 鼓励用户进行创意性思考和创作

### 技术层面
- **AI伦理实践**: 探索AI在文化批评中的正面应用
- **人机协作**: 结合人类创意和机器分析能力
- **开源精神**: 为社区提供透明、可审计的工具

## 贡献指南

我们欢迎各种形式的贡献：

- **代码贡献**: 改进算法、添加功能、修复bug
- **内容贡献**: 创作高质量的抵抗方案内容
- **理论贡献**: 提供文化批评和媒体理论支持
- **测试反馈**: 报告使用体验和改进建议

## 免责声明

本系统仅用于教育和研究目的，旨在提高用户的媒体素养和批判思维能力。所有生成的内容均为自动化分析结果，不代表开发者的个人观点。用户应独立思考，理性判断。

## 许可证

本项目采用 [MIT License](LICENSE)

---

*"让技术为文化觉醒服务，让每一次点击都成为思考的开始。"*