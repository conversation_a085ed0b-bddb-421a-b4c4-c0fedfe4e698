import mss
import cv2
import numpy as np
from PyQt5.QtWidgets import QApplication, QMainWindow
from PyQt5.QtCore import Qt, QTimer
from demo_llm import analyze_image_stream
from resources.prompt import dirty_prompt, dirty_prompt_short
import json
import sys

from overlay_manager import OverlayManager, OVERLAY_SETTINGS
from window_monitor import ApplicationMonitor
from ui_components import MouseTipWidget

class MainWindow(QMainWindow):
    """
    主窗口
    """
    def __init__(self):
        super().__init__()
        self.setGeometry(0, 0, 1, 1)
        self.setWindowFlags(Qt.Tool | Qt.WindowStaysOnTopHint)
        
        # 设置下采样率
        self.downsample_rate = 0.5
        
        # 创建遮罩管理器，设置父对象
        self.overlay_manager = OverlayManager(downsample_rate=self.downsample_rate, parent=self)
        
        # 创建小老鼠图标
        self.mouse_tip = MouseTipWidget()
        self.mouse_tip.clicked.connect(self.analyze_screen)
        self.mouse_tip.interrupt_clicked.connect(self.interrupt_processing)  # 连接中断信号
        self.mouse_tip.show()
        
        # 添加处理状态标志
        self.is_processing = False
        
        # 添加应用监控
        self.app_monitor = ApplicationMonitor()
        
        # 创建一个定时器来检查应用切换
        self.app_check_timer = QTimer()
        self.app_check_timer.timeout.connect(self.check_application)
        self.app_check_timer.start(500)  # 每500毫秒检查一次

    def interrupt_processing(self):
        """中断当前处理"""
        if self.is_processing:
            self.is_processing = False
            self.mouse_tip.stop_processing()
            print("处理已中断")
    
    def analyze_screen(self):
        if self.is_processing:  # 如果已经在处理中，直接返回
            return
            
        try:
            self.is_processing = True  # 设置处理状态
            # 开始处理时显示神鹰动画
            self.mouse_tip.start_processing()
            QApplication.processEvents()
            
            # 清除上一轮的所有遮罩
            self.overlay_manager.clear_all_overlays()
            
            with mss.mss() as sct:
                monitor = sct.monitors[1]
                img = sct.grab(monitor)
                cv_img = cv2.cvtColor(np.array(img), cv2.COLOR_BGRA2BGR)
                
                # 图像下采样
                image_array = cv2.resize(cv_img, None, fx=self.downsample_rate, fy=self.downsample_rate)
                
                # 配置
                api_key = "sk-718363330d3242e3a64e5c1aebfb8856"
                model_name = "qwen2.5-vl-72b-instruct"
                prompt = dirty_prompt # dirty_prompt_short # dirty_prompt

                # LLM
                stream = analyze_image_stream(image_array, api_key, model_name=model_name, prompt=prompt)

                print("\n开始接收响应:")
                current_dict = ""
                in_dict = False
                brace_count = 0
                
                for chunk in stream:
                    if not self.is_processing:  # 检查是否被中断
                        print("\n处理被用户中断")
                        break
                        
                    if chunk.choices[0].delta.content is not None:
                        content = chunk.choices[0].delta.content
                        print(content, end='', flush=True)
                        QApplication.processEvents()
                        
                        for char in content:
                            if not self.is_processing:  # 再次检查是否被中断
                                break
                                
                            if char == '{':
                                if not in_dict:
                                    in_dict = True
                                    current_dict = char
                                else:
                                    current_dict += char
                                brace_count += 1
                            elif char == '}':
                                brace_count -= 1
                                current_dict += char
                                if brace_count == 0 and in_dict:
                                    try:
                                        # 只处理启用的遮罩类型
                                        data = json.loads(current_dict)
                                        if isinstance(data, dict) and data.get('flag') and OVERLAY_SETTINGS.get(data.get('flag', ''), False):
                                            self.overlay_manager.process_response(current_dict)
                                        QApplication.processEvents()
                                    except Exception as e:
                                        print(f"\n处理字典时出错: {e}")
                                    current_dict = ""
                                    in_dict = False
                            elif in_dict:
                                current_dict += char
                
                print("\n响应结束")
                
        except Exception as e:
            print(f"分析屏幕时出错: {e}")
        finally:
            self.is_processing = False  # 重置处理状态
            self.mouse_tip.stop_processing()
            QApplication.processEvents()

    def check_application(self):
        """检查前台应用是否变化"""
        app_switched, current_app, template = self.app_monitor.check_app_switch()
        
        if app_switched:
            print(f"应用已切换到: {current_app}")
            
            # 清除之前的遮罩
            self.overlay_manager.clear_all_overlays()
            
            # 更新分析策略
            if current_app in self.app_monitor.app_templates:
                # 应用特定的分析策略
                self.downsample_rate = 0.5  # 可以根据应用调整下采样率
                # 其他策略调整...

def main():
    try:
        app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = MainWindow()
        main_window.show()

        # 启动应用程序事件循环
        app.exec_()
    except Exception as e:
        print(f"主程序出错: {e}")

if __name__ == '__main__':
    main()