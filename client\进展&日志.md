**2025-06-05**： 

初步完成demo，通过mss截取屏幕截图，百炼api流式调用，QT进行屏幕效果工具生成。关键延迟在3秒左右(首个遮罩产生)，其中细节为：

环境：windows，python，mss，QT

截取屏幕尺寸：2560 * 1440

参数：下采样 0.5,0.5 （qwen2.5-vl在openai协议下无法采用高质量选项，所以有单图token限制1280/次）

提示词：脏话![](https://cdn.nlark.com/yuque/0/2025/png/42911222/1749103088200-75cacdc6-9b24-47bd-9751-8370bec51229.png)

模型："qwen2.5-vl-72b-instruct" (在下采样下效果最好)

速度：首个遮罩延迟~3s，当前选用了最快的方案即流式输出+非高质量处理下的最大输入token数

![](https://cdn.nlark.com/yuque/0/2025/gif/42911222/1749087068564-4efea31f-dc8a-491b-9d6d-e9661c12abc8.gif)

**小结**：

初步完成了一个demo，采用了API的形式完成了最关键的分析，也是当前能够在相对低成本下的最快的解决方案，再快的方案处理效果不佳。首个遮罩3S也即延迟达到了3000ms，这与实时处理的理想状态相距太远，未来有如下几个路线：

1.采用UI分块+文本处理(并行可选)：如果有实时性很强的基于layout/UI的快速目标检测，配合更高效的LLM，可以绕过oneshot分析带来的巨大开销，加入并行多UI分块处理方式，来实现延迟的巨额降低，前提是针对性的训练窗口任务检测模型（YOLO-Layout, YOLO-deskUI...）,并且结合实时性较好的OCR+特殊词汇搜索快速遮罩生成+内容分析独立处理，具有高效，泛用性强的特点，缺点是需要进行一定的训练工作，有一定开发成本。

2.采用对应窗口任务的分类分析：针对HTML，直接调取不同的元素分块来预先获取UI分块信息，然后采用LLM分析文本内容，或基于分块截图采用VL分析图像内容，可以降低巨大的延迟，缺点是需要针对特定的环境平台做针对性的插件开发，无法形成统一的服务。

