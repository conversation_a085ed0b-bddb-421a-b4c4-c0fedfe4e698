"""
测试UI中的抵抗内容显示
"""

import sys
try:
    from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                               QLabel, QTabWidget, QPushButton)
    from PyQt5.QtCore import Qt
except ImportError:
    print("错误: 未找到PyQt5库，请先安装: pip install PyQt5")
    sys.exit(1)

from resources.prompt_based_generator import generate_prompt_based_content
from resources.enhanced_resistance_loader import format_enhanced_resistance_for_display_from_content

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("抵抗内容显示测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央小部件和布局
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 创建标签页控件
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane { 
                border: 1px solid #444444;
                background-color: rgba(0, 0, 0, 0.7);
                border-radius: 5px;
            }
            QTabBar::tab {
                background-color: rgba(50, 50, 50, 0.8);
                color: white;
                padding: 5px;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
            }
            QTabBar::tab:selected {
                background-color: rgba(100, 0, 0, 0.8);
            }
        """)
        
        # 创建测试按钮
        test_button = QPushButton("生成测试内容")
        test_button.clicked.connect(self.generate_test_content)
        layout.addWidget(test_button)
        
        layout.addWidget(self.tab_widget)
        
        # 添加状态提示
        self.status_label = QLabel("点击按钮生成测试内容")
        layout.addWidget(self.status_label)
    
    def generate_test_content(self):
        """生成测试内容并显示"""
        self.status_label.setText("正在生成内容，请稍候...")
        QApplication.processEvents()
        
        # 清除现有标签页
        while self.tab_widget.count() > 0:
            self.tab_widget.removeTab(0)
        
        # 创建模拟的污染数据
        test_pollution_data = {
            "type": "CELEBRITY",
            "keywords": ["明星", "娱乐", "热搜"],
            "comment": "某明星在社交媒体上发布了新动态，引发大量讨论",
            "raw_description": "这是一条关于明星的热搜信息"
        }
        
        # 生成不同类型的抵抗内容
        content_types = [
            ("philosophy", "哲学反思"),
            ("economics", "经济分析"), 
            ("creative", "创意转换"), 
            ("historical", "历史脉络")
        ]
        
        for content_type, tab_name in content_types:
            # 生成内容
            self.status_label.setText(f"正在生成 {tab_name} 内容...")
            QApplication.processEvents()
            
            content = generate_prompt_based_content(content_type, test_pollution_data)
            
            # 创建标签页
            tab = QWidget()
            tab_layout = QVBoxLayout(tab)
            
            # 添加标题标签
            title_label = QLabel(content.get("title", "未知标题"))
            title_label.setStyleSheet("""
                color: white;
                font-size: 14px;
                font-weight: bold;
                background-color: rgba(150, 0, 0, 0.8);
                padding: 5px;
                border-radius: 5px;
            """)
            title_label.setWordWrap(True)
            tab_layout.addWidget(title_label)
            
            # 添加内容标签
            content_text = content.get("content", "")
            content_label = QLabel(content_text)
            content_label.setStyleSheet("""
                color: white;
                font-size: 12px;
                background-color: rgba(0, 0, 0, 0.7);
                padding: 5px;
                border-radius: 5px;
            """)
            content_label.setWordWrap(True)
            tab_layout.addWidget(content_label)
            
            # 添加资源标签
            resources = content.get("resources", [])
            if resources:
                resources_title = QLabel("推荐阅读:")
                resources_title.setStyleSheet("""
                    color: white;
                    font-size: 12px;
                    font-weight: bold;
                """)
                tab_layout.addWidget(resources_title)
                
                for resource in resources:
                    resource_label = QLabel(f"• {resource}")
                    resource_label.setStyleSheet("""
                        color: white;
                        font-size: 12px;
                    """)
                    resource_label.setWordWrap(True)
                    tab_layout.addWidget(resource_label)
            
            # 添加到标签页
            self.tab_widget.addTab(tab, tab_name)
        
        self.status_label.setText("内容生成完成")

if __name__ == "__main__":
    print("启动UI测试程序...")
    app = QApplication(sys.argv)
    window = TestWindow()
    window.show()
    print("窗口已显示，等待用户操作")
    sys.exit(app.exec_()) 