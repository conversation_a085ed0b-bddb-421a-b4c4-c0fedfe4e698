import os
import base64
import requests
import numpy as np
from PIL import Image
import io
import matplotlib.pyplot as plt
from openai import OpenAI
from pathlib import Path
from resources.prompt import dirty_prompt

def get_upload_policy(api_key, model_name):
    """获取文件上传凭证"""
    url = "https://dashscope.aliyuncs.com/api/v1/uploads"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    params = {
        "action": "getPolicy",
        "model": model_name
    }
    
    response = requests.get(url, headers=headers, params=params)
    if response.status_code != 200:
        raise Exception(f"Failed to get upload policy: {response.text}")
    
    return response.json()['data']

def upload_file_to_oss(policy_data, file_path):
    """将文件上传到临时存储OSS"""
    file_name = Path(file_path).name
    key = f"{policy_data['upload_dir']}/{file_name}"
    
    with open(file_path, 'rb') as file:
        files = {
            'OSSAccessKeyId': (None, policy_data['oss_access_key_id']),
            'Signature': (None, policy_data['signature']),
            'policy': (None, policy_data['policy']),
            'x-oss-object-acl': (None, policy_data['x_oss_object_acl']),
            'x-oss-forbid-overwrite': (None, policy_data['x_oss_forbid_overwrite']),
            'key': (None, key),
            'success_action_status': (None, '200'),
            'file': (file_name, file)
        }
        
        response = requests.post(policy_data['upload_host'], files=files)
        if response.status_code != 200:
            raise Exception(f"Failed to upload file: {response.text}")
    
    return f"oss://{key}"

def upload_file_and_get_url(api_key, model_name, file_path):
    """上传文件并获取公网URL"""
    # 1. 获取上传凭证
    policy_data = get_upload_policy(api_key, model_name) 
    # 2. 上传文件到OSS
    oss_url = upload_file_to_oss(policy_data, file_path)
    
    return oss_url

def read_image_to_base64(file_path):
    """读取图片并转换为base64格式"""
    try:
        with open(file_path, 'rb') as image_file:
            # 读取图片文件
            image_data = image_file.read()
            # 转换为base64
            base64_data = base64.b64encode(image_data).decode('utf-8')
            return base64_data
    except Exception as e:
        print(f"Error reading image: {e}")
        return None

def image_array_to_base64(image_array):
    """将numpy数组转换为base64格式"""
    try:
        # 确保图像数组是uint8类型
        if image_array.dtype != np.uint8:
            image_array = (image_array * 255).astype(np.uint8)
        
        # 将numpy数组转换为PIL图像
        if len(image_array.shape) == 3 and image_array.shape[2] == 3:
            image = Image.fromarray(image_array)
        else:
            raise ValueError("Image array must be RGB (3 channels)")
        
        # 将图像转换为JPEG格式的字节流
        img_byte_arr = io.BytesIO()
        image.save(img_byte_arr, format='JPEG')
        img_byte_arr = img_byte_arr.getvalue()
        
        # 转换为base64
        base64_data = base64.b64encode(img_byte_arr).decode('utf-8')
        return base64_data
    except Exception as e:
        print(f"Error converting image array to base64: {e}")
        return None

def analyze_image_stream(image_array, api_key, model_name="qwen2.5-vl-72b-instruct", prompt=None):
    """
    分析图像并返回流式输出
    
    参数:
        image_array: numpy数组格式的图像数据 (RGB格式)
        api_key: API密钥
        model_name: 模型名称
        prompt: 可选的提示文本，如果为None则使用默认提示
    
    返回:
        生成器对象，用于流式获取响应
    """
    # 默认提示文本
    default_prompt = prompt

    # 将图像数组转换为base64
    image_base64 = image_array_to_base64(image_array)
    if not image_base64:
        raise ValueError("Failed to convert image array to base64")

    # 初始化OpenAI客户端
    client = OpenAI(
        api_key=api_key,
        base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
    )

    # 构建消息
    messages = [
        {
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": prompt if prompt else default_prompt
                },
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpeg;base64,{image_base64}",
                        "detail": "high"
                    }
                }
            ]
        }
    ]

    # 创建流式请求
    stream = client.chat.completions.create(
        model=model_name,
        messages=messages,
        stream=True
    )

    # 返回生成器对象
    return stream

def show_image(image_array, title="Image Preview"):
    """显示图片预览"""
    plt.figure(figsize=(10, 8))
    plt.imshow(image_array)
    plt.title(title)
    plt.axis('off')  # 关闭坐标轴
    plt.show()

# 使用示例
if __name__ == "__main__":
    # 配置
    api_key = "sk-718363330d3242e3a64e5c1aebfb8856"

    # 模型名称
    model_name = "qwen2.5-vl-32b-instruct"

    # 读取测试图片
    test_image_path = r"resources\news_testpic11.jpg"

    # 提示词 
    prompt = dirty_prompt
    
    try:
        # 使用PIL读取图片并转换为numpy数组
        image = Image.open(test_image_path)
        image_array = np.array(image)
        
        # 显示图片预览
        print("显示图片预览...")
        show_image(image_array, f"Image from {test_image_path}")
        
        # 获取流式输出
        stream = analyze_image_stream(image_array, api_key, model_name=model_name, prompt=prompt)
        
        # 打印响应
        print("\n开始接收响应:")
        for chunk in stream:
            if chunk.choices[0].delta.content is not None:
                print(chunk.choices[0].delta.content, end='', flush=True)
        print("\n响应结束")
        
    except Exception as e:
        print(f"Error in main: {e}")



